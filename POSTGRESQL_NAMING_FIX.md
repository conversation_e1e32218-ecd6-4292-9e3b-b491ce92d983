# PostgreSQL Naming Consistency Fix

## 🔍 Problem Identified

Your PostgreSQL pod was sometimes appearing as `postgresql` and sometimes as `warda-postgresql` due to **inconsistent naming** throughout your Helm templates.

## 🎯 Root Cause

The issue was caused by:

1. **Conflicting Override**: `fullnameOverride: "postgresql"` in PostgreSQL values.yaml
2. **Mixed References**: Some templates used `postgresql`, others used `{{ .Release.Name }}-postgresql`
3. **Inconsistent Secret Names**: Secret was named `{{ .Release.Name }}-postgresql` but service was `postgresql`

## ✅ Solution Applied

**Standardized on `warda-postgresql` naming** by:

### 1. Removed the Override
```yaml
# Before
fullnameOverride: "postgresql"

# After
# fullnameOverride: "postgresql"  # Removed to use standard naming: warda-postgresql
```

### 2. Fixed All Service References
Updated all hardcoded `postgresql` references to use `{{ .Release.Name }}-postgresql` or proper templates:

**Files Updated:**
- `helm/warda/charts/postgresql/templates/postgresql-secret.yaml`
- `helm/warda/charts/postgresql/templates/deployment.yaml`
- `helm/warda/charts/backend/templates/configmap.yaml`
- `helm/warda/charts/backend/templates/deployment.yaml`
- `helm/warda/charts/migrate/templates/migrate-job.yaml`
- `helm/warda/charts/keycloak/templates/keycloak-init-configmap.yaml`
- `helm/warda/charts/keycloak/values.yaml`
- `helm/warda/values.yaml`

### 3. Consistent Naming Pattern
Now everything uses the standard Helm naming convention:
- **Service Name**: `warda-postgresql`
- **Secret Name**: `warda-postgresql`
- **Pod Name**: `warda-postgresql-xxx`

## 🔧 What Changed

### Before (Inconsistent)
```yaml
# Some places used:
host: postgresql
name: postgresql
# Others used:
name: {{ .Release.Name }}-postgresql
secretKeyRef:
  name: warda-postgresql
```

### After (Consistent)
```yaml
# All places now use:
host: warda-postgresql
name: {{ .Release.Name }}-postgresql
secretKeyRef:
  name: {{ include "postgresql.fullname" . }}
```

## 🎉 Benefits

1. **Predictable Naming**: PostgreSQL will always be `warda-postgresql`
2. **No More Confusion**: All references point to the same service
3. **Helm Best Practices**: Uses standard Helm naming conventions
4. **Easier Debugging**: Consistent names across all resources

## 🚀 Verification

After deploying, you should see:
```bash
kubectl get pods
# warda-postgresql-xxx

kubectl get svc
# warda-postgresql

kubectl get secrets
# warda-postgresql
```

## 📋 Testing

To verify the fix works:
```bash
# Deploy with the fixes
make keycloak-setup

# Check all resources use consistent naming
kubectl get all -l app.kubernetes.io/name=postgresql

# Verify connectivity
kubectl exec -it deployment/warda-backend -- ping warda-postgresql
```

The PostgreSQL naming inconsistency is now resolved! 🎯
