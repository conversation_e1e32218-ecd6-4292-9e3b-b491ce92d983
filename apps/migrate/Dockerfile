# Use the official Rust image for building
FROM rust:1.88.0-slim-bookworm AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

# Create and set the working directory
WORKDIR /app

# Copy only the necessary files for dependency resolution
COPY crates/shared ./crates/shared
COPY apps/migrate/Cargo.toml ./apps/migrate/

# Create a dummy main.rs for initial build
RUN mkdir -p apps/migrate/src && \
    echo 'fn main() {}' > apps/migrate/src/main.rs

# Change to the migrate directory for building
WORKDIR /app/apps/migrate

# Do an initial build to cache dependencies
RUN cargo build --release

# Now copy the actual source code and migrations
COPY crates/shared/src ../../crates/shared/src/
COPY apps/migrate/src ./src/
COPY apps/migrate/migrations ./migrations/

# Touch the main file to force a rebuild
RUN touch src/main.rs

# Build the application
RUN cargo build --release

# Final image
FROM debian:bookworm-slim AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

# Copy the binary from the builder
COPY --from=builder /app/apps/migrate/target/release/migrate /usr/local/bin/

# Set the entrypoint
ENTRYPOINT ["/usr/local/bin/migrate"]
