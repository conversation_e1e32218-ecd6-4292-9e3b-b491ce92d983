-- Create Keycloak user and prepare for database creation
-- This migration sets up the user infrastructure for Keycloak
-- Note: Database creation will be handled by the init container

-- Create keycloak user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_user WHERE usename = 'keycloak') THEN
        CREATE USER keycloak WITH PASSWORD 'keycloak_password';
        RAISE NOTICE 'Created keycloak user';
    ELSE
        RAISE NOTICE 'Keycloak user already exists';
    END IF;
END
$$;

-- Create a table to track Keycloak setup status
CREATE TABLE IF NOT EXISTS keycloak_setup_status (
    id SERIAL PRIMARY KEY,
    setup_completed BOOLEAN DEFAULT FALSE,
    setup_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial status if not exists
INSERT INTO keycloak_setup_status (setup_completed)
SELECT FALSE
WHERE NOT EXISTS (SELECT 1 FROM keycloak_setup_status);

-- Note: The actual keycloak database will be created by an init container
-- that runs with superuser privileges before Keycloak starts
