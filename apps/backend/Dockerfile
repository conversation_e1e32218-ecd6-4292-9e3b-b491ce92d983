# Use the official Rust image for building
FROM rust:1.88.0-slim-bookworm AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

# Create and set the working directory
WORKDIR /app

# Copy only the necessary files for dependency resolution
COPY crates/shared ./crates/shared
COPY apps/backend/Cargo.toml ./apps/backend/

# Create a dummy main.rs for initial build
RUN mkdir -p apps/backend/src && \
    echo 'fn main() {}' > apps/backend/src/main.rs

# Change to the backend directory for building
WORKDIR /app/apps/backend

# Do an initial build to cache dependencies
RUN cargo build --release

# Now copy the actual source code
COPY crates/shared/src ../../crates/shared/src/
COPY apps/backend/src ./src/

# Touch the main file to force a rebuild
RUN touch src/main.rs

# Build the application
RUN cargo build --release

FROM debian:bookworm-slim AS runtime
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/apps/backend/target/release/backend /usr/local/bin/
ENTRYPOINT ["/usr/local/bin/backend"]
