use walkers::{HttpTiles, Map, MapMemory, sources::OpenStreetMap, sources::Mapbox, lon_lat};
use egui::{Context, CentralPanel};
use eframe::{App, Frame};

/// Shared data that all tabs can access and modify
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct SharedData {
    pub counter: i32,
    pub text: String,
    pub slider_value: f32,
    pub checkbox_state: bool,
    pub items: Vec<String>,
    
    // Map-related shared state
    #[serde(skip)]
    pub tiles: Option<HttpTiles>,
    #[serde(skip)]
    pub map_memory: MapMemory,
}

impl Default for SharedData {
    fn default() -> Self {
        Self {
            counter: 0,
            text: "Shared text across tabs".to_owned(),
            slider_value: 5.0,
            checkbox_state: false,
            items: vec!["Item 1".to_owned(), "Item 2".to_owned()],
            tiles: None,
            map_memory: MapMemory::default(),
        }
    }
}

/// Available tabs/views
#[derive(<PERSON><PERSON>, Copy, PartialEq, serde::Deserialize, serde::Serialize)]
pub enum Tab {
    Map,
    Counter,
    TextEditor,
    DataList,
}

impl Default for Tab {
    fn default() -> Self {
        Tab::Counter
    }
}

impl Tab {
    fn name(&self) -> &'static str {
        match self {
            Tab::Map => "Map",
            Tab::Counter => "Counter",
            Tab::TextEditor => "Text Editor",
            Tab::DataList => "Data List",
        }
    }
}

/// Main application with tab-based UI
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct TemplateApp {
    pub shared_data: SharedData,
    pub current_tab: Tab,
}

impl Default for TemplateApp {
    fn default() -> Self {
        Self {
            shared_data: SharedData::default(),
            current_tab: Tab::default(),
        }
    }
}

impl TemplateApp {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        log::info!("Initializing TemplateApp");
        
        let mut app: Self = if let Some(storage) = cc.storage {
            log::debug!("Loading app state from storage");
            eframe::get_value(storage, eframe::APP_KEY).unwrap_or_default()
        } else {
            log::debug!("No storage available, using default state");
            Default::default()
        };

        // Try to get Mapbox token from environment, fallback to OpenStreetMap
        log::info!("mapbox access token: {:#?}",std::env::var("MAPBOX_ACCESS_TOKEN"));
        let tiles = if let Ok(token) = std::env::var("MAPBOX_ACCESS_TOKEN") {
            if !token.is_empty() {
                log::info!("Using Mapbox tiles with provided token");
                HttpTiles::new(Mapbox { style: Default::default(), high_resolution: false, access_token: token }, cc.egui_ctx.clone())
            } else {
                log::warn!("MAPBOX_ACCESS_TOKEN is empty, falling back to OpenStreetMap");
                HttpTiles::new(OpenStreetMap, cc.egui_ctx.clone())
            }
        } else {
            log::info!("No MAPBOX_ACCESS_TOKEN found, using OpenStreetMap");
            HttpTiles::new(OpenStreetMap, cc.egui_ctx.clone())
        };

        app.shared_data.tiles = Some(tiles);
        log::debug!("TemplateApp initialized successfully");
        
        app
    }
}

impl App for TemplateApp {
    fn update(&mut self, ctx: &Context, _frame: &mut Frame) {
        // Log tab switches
        static mut LAST_TAB: Option<Tab> = None;
        unsafe {
            if LAST_TAB != Some(self.current_tab) {
                log::debug!("Switched to tab: {}", self.current_tab.name());
                LAST_TAB = Some(self.current_tab);
            }
        }

        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            egui::MenuBar::new().ui(ui, |ui| {
                let is_web = cfg!(target_arch = "wasm32");
                if !is_web {
                    ui.menu_button("File", |ui| {
                        if ui.button("Quit").clicked() {
                            log::info!("User requested quit");
                            ctx.send_viewport_cmd(egui::ViewportCommand::Close);
                        }
                    });
                    ui.add_space(16.0);
                }

                egui::widgets::global_theme_preference_buttons(ui);
            });
        });

        // Tab bar
        egui::TopBottomPanel::top("tab_bar").show(ctx, |ui| {
            ui.horizontal(|ui| {
                ui.spacing_mut().item_spacing.x = 0.0;
                
                for tab in [Tab::Counter, Tab::TextEditor, Tab::DataList, Tab::Map] {
                    let selected = self.current_tab == tab;
                    if ui.selectable_label(selected, tab.name()).clicked() {
                        log::debug!("User clicked tab: {}", tab.name());
                        self.current_tab = tab;
                    }
                }
            });
        });

        // Main content area
        CentralPanel::default().show(ctx, |ui| {
            match self.current_tab {
                Tab::Counter => show_counter_view(ui, &mut self.shared_data),
                Tab::TextEditor => show_text_editor_view(ui, &mut self.shared_data),
                Tab::DataList => show_data_list_view(ui, &mut self.shared_data),
                Tab::Map => show_map_view(ui, &mut self.shared_data),
            }
        });
    }

    fn save(&mut self, storage: &mut dyn eframe::Storage) {
        log::debug!("Saving app state to storage");
        eframe::set_value(storage, eframe::APP_KEY, self);
    }
}

// View functions for each tab

fn show_counter_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Counter View");
    ui.separator();
    
    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
    });
    
    ui.horizontal(|ui| {
        if ui.button("Increment").clicked() {
            shared_data.counter += 1;
            log::debug!("Counter incremented to: {}", shared_data.counter);
        }
        if ui.button("Decrement").clicked() {
            shared_data.counter -= 1;
            log::debug!("Counter decremented to: {}", shared_data.counter);
        }
        if ui.button("Reset").clicked() {
            log::info!("Counter reset from {} to 0", shared_data.counter);
            shared_data.counter = 0;
        }
    });
    
    ui.separator();
    
    ui.label("Shared slider (affects other tabs):");
    let old_value = shared_data.slider_value;
    ui.add(egui::Slider::new(&mut shared_data.slider_value, 0.0..=10.0).text("value"));
    if (old_value - shared_data.slider_value).abs() > 0.01 {
        log::debug!("Slider value changed from {:.1} to {:.1}", old_value, shared_data.slider_value);
    }
    
    let old_checkbox = shared_data.checkbox_state;
    ui.checkbox(&mut shared_data.checkbox_state, "Shared checkbox");
    if old_checkbox != shared_data.checkbox_state {
        log::debug!("Checkbox toggled to: {}", shared_data.checkbox_state);
    }
    
    ui.separator();
    ui.label("💡 Try switching tabs to see how state persists!");
}

fn show_text_editor_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Text Editor View");
    ui.separator();
    
    ui.label("This text is shared across all tabs:");
    ui.text_edit_multiline(&mut shared_data.text);
    
    ui.separator();
    
    ui.horizontal(|ui| {
        ui.label("Counter from other tab:");
        ui.label(shared_data.counter.to_string());
        if ui.button("+1").clicked() {
            shared_data.counter += 1;
        }
    });
    
    ui.horizontal(|ui| {
        ui.label("Slider value:");
        ui.label(format!("{:.1}", shared_data.slider_value));
    });
    
    if shared_data.checkbox_state {
        ui.colored_label(egui::Color32::GREEN, "✓ Checkbox is checked!");
    } else {
        ui.colored_label(egui::Color32::RED, "✗ Checkbox is unchecked");
    }
}

fn show_data_list_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Data List View");
    ui.separator();
    
    ui.horizontal(|ui| {
        ui.label("Add item:");
        let mut new_item = String::new();
        if ui.text_edit_singleline(&mut new_item).lost_focus() 
            && ui.input(|i| i.key_pressed(egui::Key::Enter)) 
            && !new_item.is_empty() {
            log::info!("Added new item: '{}'", new_item);
            shared_data.items.push(new_item);
        }
    });
    
    ui.separator();
    
    ui.label("Items:");
    let mut to_remove = None;
    for (i, item) in shared_data.items.iter().enumerate() {
        ui.horizontal(|ui| {
            ui.label(format!("{}. {}", i + 1, item));
            if ui.small_button("Remove").clicked() {
                log::info!("Removing item: '{}'", item);
                to_remove = Some(i);
            }
        });
    }
    
    if let Some(index) = to_remove {
        shared_data.items.remove(index);
    }
    
    ui.separator();
    
    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
        ui.label("| Slider:");
        ui.label(format!("{:.1}", shared_data.slider_value));
    });
}

fn show_map_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Map View");
    ui.separator();
    
    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
        if ui.button("Add marker count").clicked() {
            shared_data.counter += 1;
            log::debug!("Map view: Counter incremented to {}", shared_data.counter);
        }
    });
    
    ui.separator();
    
    if let Some(ref mut tiles) = shared_data.tiles {
        ui.add(Map::new(
            Some(tiles),
            &mut shared_data.map_memory,
            lon_lat(17.03664, 51.09916)
        ));
    } else {
        log::error!("Map tiles not initialized");
        ui.label("Map tiles not initialized");
    }
}