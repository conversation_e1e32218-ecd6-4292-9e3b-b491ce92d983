# Build stage
FROM rust:1.88-slim AS builder

# Install required tools
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Install wasm32 target and trunk
RUN rustup target add wasm32-unknown-unknown && \
    cargo install --locked trunk wasm-bindgen-cli

# Enable getrandom’s wasm_js backend during compilation
ENV RUSTFLAGS='--cfg getrandom_backend="wasm_js"'

# Set working directory
WORKDIR /app

# Copy only dependency files first for better caching
COPY apps/frontend/Cargo.toml apps/frontend/Cargo.lock* ./apps/frontend/

# Create dummy source files to cache dependencies
RUN mkdir -p apps/frontend/src && \
    echo "fn main() {}" > apps/frontend/src/main.rs && \
    echo "pub mod app; pub use app::TemplateApp;" > apps/frontend/src/lib.rs && \
    echo "pub struct TemplateApp; impl eframe::App for TemplateApp { fn update(&mut self, _: &egui::Context, _: &mut eframe::Frame) {} }" > apps/frontend/src/app.rs

# Build dependencies only
WORKDIR /app/apps/frontend
RUN cargo build --target=wasm32-unknown-unknown --release

# Now copy the actual source code
COPY apps/frontend/src ./src/
COPY apps/frontend/index.html ./
COPY apps/frontend/assets ./assets/
COPY apps/frontend/Trunk.toml* ./

# Touch main files to force rebuild of actual code
RUN touch src/main.rs src/lib.rs

# Build the actual application
RUN trunk build --release

# Final stage
FROM nginx:alpine

# Copy the built files from the builder stage
COPY --from=builder /app/apps/frontend/dist /usr/share/nginx/html

# Copy nginx config
COPY apps/frontend/nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
