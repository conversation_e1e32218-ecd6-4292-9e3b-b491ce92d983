#APP_NAMES := migrate backend frontend
APP_NAMES := migrate backend frontend
DOCKER_REPO := warda 

.PHONY: all build docker-build docker-push helm-deploy helm-delete minikube-setup minikube-build

minikube-init:
	@echo "🔧 Ensuring Helm is ready and charts are pulled..."
	helm repo add grafana https://grafana.github.io/helm-charts || true
	helm repo update
	helm plugin install https://github.com/chartmuseum/helm-push || true

	@echo "🚀 Starting Minikube..."
	minikube start --driver=docker
	minikube addons enable ingress || true

	@echo "📦 Updating Helm dependencies..."
	cd helm/warda && helm dependency update

	@echo "✅ Minikube init complete. You can now run 'make minikube-build'"

# ---- Build All Rust Apps ----
build:
	cargo build --release

# ---- Minikube Setup ----
minikube-start:
	@if ! minikube status | grep -q "Running"; then \
		echo "🚀 Starting Minikube..."; \
		eval $(minikube -p minikube docker-env) minikube start --driver=docker; \
		eval $(minikube -p minikube docker-env) minikube addons enable ingress || true \
	else \
		echo "✅ Minikube already running."; \
	fi

minikube-build: minikube-start
	@echo "🐳 Building Docker images in Minikube's Docker daemon..."
	@eval $$(minikube -p minikube docker-env) && \
	for app in $(APP_NAMES); do \
		echo "🛠️  Building $$app..."; \
		docker build \
			-f apps/$$app/Dockerfile \
			-t warda/$$app:latest \
			. && \
		echo "✅ Built warda/$$app:latest in Minikube's Docker daemon"; \
	done
	@echo "📋 Available images in Minikube's Docker daemon:"
	@eval $$(minikube -p minikube docker-env) && docker images | grep -E 'warda/|REPOSITORY' || echo "No warda images found"

# ---- Build and Deploy Without Restarting Minikube ----
minikube-build-deploy: minikube-build
	$(MAKE) helm-deploy

# ---- Helm Deploy ----
helm-deploy:
	helm upgrade --install warda helm/warda -f helm/warda/values.yaml
	@for app in $(APP_NAMES); do \
		kubectl rollout restart deployment $$app || true ; \
	done

# ---- Helm Teardown ----
helm-delete:
	helm uninstall warda

# ---- Minikube Dashboard ----
dashboard:
	minikube dashboard

# ---- Open Grafana via Minikube ----
grafana:
	@echo "🚀 Opening Grafana UI via Minikube..."
	minikube service warda-grafana

# ---- Local Docker Builds ----
docker-build:

# ---- Push (optional, for remote clusters) ----
docker-push:
	@for app in $(APP_NAMES); do \
		docker tag $(DOCKER_REPO)/$$app:latest $(DOCKER_REPO)/$$app:latest ; \
		docker push $(DOCKER_REPO)/$$app:latest ; \
	done

# ---- Open Key Services via Minikube ----
minikube-service:
	minikube dashboard & \
	minikube service backend warda-grafana
