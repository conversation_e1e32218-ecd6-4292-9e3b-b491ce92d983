# Global configuration
global:
  postgresql:
    postgresqlUsername: postgres
    postgresqlPassword: postgres_password
    postgresqlDatabase: postgresql
    service:
      port: 5432
    persistence:
      enabled: true
      size: 8Gi

# PostgreSQL chart configuration
postgresql:
  enabled: true
  # Use values from global configuration
  postgresqlUsername: postgres
  postgresqlPassword: postgres_password
  postgresqlDatabase: postgresql
  service:
    port: 5432
  persistence:
    enabled: true
    size: 8Gi

# Keycloak configuration
keycloak:
  enabled: true

  # Admin credentials
  admin:
    username: admin
    password: admin123

  # Database configuration
  database:
    host: warda-postgresql
    port: 5432
    name: keycloak
    username: keycloak
    password: keycloak_password

  # Hostname configuration
  hostname: keycloak.local

  # Ingress configuration
  ingress:
    enabled: true
    hosts:
      - host: keycloak.local
        paths:
          - path: /
            pathType: Prefix

  # Realm and client configuration
  realm:
    name: warda
    displayName: "Warda Platform"
    frontendClient:
      clientId: warda-frontend
      redirectUris:
        - "http://localhost:3000/*"
        - "http://frontend.local/*"
      webOrigins:
        - "http://localhost:3000"
        - "http://frontend.local"
    backendClient:
      clientId: warda-backend
      secret: backend-client-secret

frontend:
  enabled: true
  # Mapbox configuration
  mapboxToken: "pk.eyJ1Ijoic2lldHNlbSIsImEiOiJjbWR1M2NmajIxNXNxMmtyMzIzenZwbW1mIn0.isXf_tnxfA4aBS3NeJxJkA"
  # Keycloak configuration
  keycloak:
    url: "http://keycloak.local"
    realm: "warda"
    clientId: "warda-frontend"

# Backend configuration
backend:
  enabled: true
  # Database URL for the backend
  databaseUrl: "***********************************************************/postgresql"

  image:
    repository: warda/backend
    tag: latest
    pullPolicy: IfNotPresent
  env:
    RUST_LOG: "warda=debug,backend=debug,tower_http=debug"
  # Database configuration
  database:
    host: "warda-postgresql"
    port: 5432
    name: postgresql
    user: postgres
  # OAuth/Keycloak configuration
  oauth:
    keycloak:
      url: "http://keycloak:8080"
      realm: "warda"
      clientId: "warda-backend"
      issuer: "http://keycloak:8080/realms/warda"

# Migrate configuration
migrate:
  enabled: true
  # Use a separate image for migrations
  image:
    repository: warda/migrate
    tag: latest
    pullPolicy: IfNotPresent
  # Wait container configuration
  waitContainer:
    image: alpine
    tag: "3.18"