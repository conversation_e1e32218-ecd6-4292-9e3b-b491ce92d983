# Global configuration
global:
  postgresql:
    postgresqlUsername: postgres
    postgresqlPassword: postgres_password
    postgresqlDatabase: postgresql
    service:
      port: 5432
    persistence:
      enabled: true
      size: 8Gi

# PostgreSQL chart configuration
postgresql:
  enabled: true
  # Use values from global configuration
  postgresqlUsername: postgres
  postgresqlPassword: postgres_password
  postgresqlDatabase: postgresql
  service:
    port: 5432
  persistence:
    enabled: true
    size: 8Gi

frontend:
  # Mapbox configuration
  mapboxToken: "pk.eyJ1Ijoic2lldHNlbSIsImEiOiJjbWR1M2NmajIxNXNxMmtyMzIzenZwbW1mIn0.isXf_tnxfA4aBS3NeJxJkA"

# Backend configuration
backend:
  enabled: true
  # Database URL for the backend
  databaseUrl: "*****************************************************/postgresql"
  
  image:
    repository: warda/backend
    tag: latest
    pullPolicy: IfNotPresent
  env:
    RUST_LOG: "warda=debug,backend=debug,tower_http=debug"
  # Database configuration
  database:
    host: "postgresql"
    port: 5432
    name: postgresql
    user: postgres

# Migrate configuration
migrate:
  enabled: true
  # Use a separate image for migrations
  image:
    repository: warda/migrate
    tag: latest
    pullPolicy: IfNotPresent
  # Wait container configuration
  waitContainer:
    image: alpine
    tag: "3.18"