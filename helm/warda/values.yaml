# Global configuration
global:
  postgresql:
    postgresqlUsername: postgres
    postgresqlPassword: postgres_password
    postgresqlDatabase: postgresql
    service:
      port: 5432
    persistence:
      enabled: true
      size: 8Gi

# PostgreSQL chart configuration
postgresql:
  enabled: true
  # Use values from global configuration
  postgresqlUsername: postgres
  postgresqlPassword: postgres_password
  postgresqlDatabase: postgresql
  service:
    port: 5432
  persistence:
    enabled: true
    size: 8Gi

# Keycloak configuration
keycloak:
  enabled: true
  
  # Image configuration
  image:
    repository: quay.io/keycloak/keycloak
    tag: "23.0.0"
    pullPolicy: IfNotPresent
  
  # Environment variables
  env:
    # Database configuration
    KC_DB: postgres
    KC_DB_URL: ************************************************
    KC_DB_USERNAME: keycloak
    KC_DB_PASSWORD: keycloak_password
    KC_DB_SCHEMA: public
    
    # Database driver configuration
    KC_DB_DRIVER: postgres
    KC_TRANSACTION_XA_ENABLED: "false"
    
    # Hostname and HTTP settings
    KC_HOSTNAME: keycloak.local
    KC_HOSTNAME_STRICT: "false"
    KC_HTTP_ENABLED: "true"
    
    # Proxy settings for development
    KC_PROXY: edge
    
    # Cache configuration
    KC_CACHE: local
    KC_CACHE_CONFIG_FILE: cache-ispn.xml
    
    # Additional JVM options
    JAVA_OPTS: "-Dquarkus.datasource.db-kind=postgresql -Dquarkus.datasource.jdbc.driver=org.postgresql.Driver -Dquarkus.hibernate-orm.dialect=org.hibernate.dialect.PostgreSQLDialect"
  
  # Admin user configuration
  admin:
    username: admin
    password: admin123
  
  # Hostname configuration
  hostname:
    hostname: keycloak.local
    strict: false
  
  # Ingress configuration
  ingress:
    enabled: true
    hosts:
      - host: keycloak.local
        paths:
          - path: /
            pathType: Prefix
  
  # Realm configuration
  realm:
    name: warda
    displayName: "Warda Platform"
    frontendClient:
      clientId: warda-frontend
      redirectUris:
        - "http://localhost:3000/*"
        - "http://frontend.local/*"
      webOrigins:
        - "http://localhost:3000"
        - "http://frontend.local"
    backendClient:
      clientId: warda-backend
      secret: backend-client-secret

frontend:
  enabled: true
  # Mapbox configuration
  mapboxToken: "pk.eyJ1Ijoic2lldHNlbSIsImEiOiJjbWR1M2NmajIxNXNxMmtyMzIzenZwbW1mIn0.isXf_tnxfA4aBS3NeJxJkA"
  # Keycloak configuration
  keycloak:
    url: "http://keycloak.local"
    realm: "warda"
    clientId: "warda-frontend"

# Backend configuration
backend:
  enabled: true
  # Database URL for the backend
  databaseUrl: "***********************************************************/postgresql"

  image:
    repository: warda/backend
    tag: latest
    pullPolicy: IfNotPresent
  env:
    RUST_LOG: "warda=debug,backend=debug,tower_http=debug"
  # Database configuration
  database:
    host: "warda-postgresql"
    port: 5432
    name: postgresql
    user: postgres
  # OAuth/Keycloak configuration
  oauth:
    keycloak:
      url: "http://keycloak:8080"
      realm: "warda"
      clientId: "warda-backend"
      issuer: "http://keycloak:8080/realms/warda"

# Migrate configuration
migrate:
  enabled: true
  # Use a separate image for migrations
  image:
    repository: warda/migrate
    tag: latest
    pullPolicy: IfNotPresent
  # Wait container configuration
  waitContainer:
    image: alpine
    tag: "3.18"