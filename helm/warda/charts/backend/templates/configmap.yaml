apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-backend-config
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  config.toml: |
    [server]
    host = "0.0.0.0"
    port = 8080

    [database]
    url = "postgres://postgres:{{ .Values.global.postgresql.postgresqlPassword }}@postgresql:{{ .Values.global.postgresql.service.port }}/{{ .Values.global.postgresql.postgresqlDatabase }}"
    
    [log]
    level = "debug"