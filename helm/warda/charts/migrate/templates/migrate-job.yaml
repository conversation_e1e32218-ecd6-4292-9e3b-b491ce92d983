apiVersion: batch/v1
kind: Job
metadata:
  name: db-migrate
  labels:
    app.kubernetes.io/name: {{ include "migrate.name" . }}
    helm.sh/chart: {{ include "migrate.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  backoffLimit: 1
  template:
    spec:
      restartPolicy: Never
      serviceAccountName: {{ .Release.Name }}-migrate
      initContainers:
        - name: wait-for-postgres
          image: {{ .Values.waitContainer.image }}:{{ .Values.waitContainer.tag }}
          command: 
            - 'sh'
            - '-c'
            - |
              # Install required tools
              apk add --no-cache postgresql-client netcat-openbsd
              
              # Wait for PostgreSQL service at {{ .Release.Name }}-postgresql:5432...
              echo "Waiting for PostgreSQL service at {{ .Release.Name }}-postgresql:5432..."
              until nc -z postgresql 5432; do
                echo "Waiting for PostgreSQL service to be available...";
                sleep 2;
              done
              
              # Wait for PostgreSQL to accept connections
              echo "Waiting for PostgreSQL to accept connections..."
              until pg_isready -h postgresql -p 5432 -U postgres; do
                echo "PostgreSQL not yet accepting connections, retrying...";
                sleep 2;
              done
              
              echo "PostgreSQL is ready!"
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: postgres-password
            - name: PGHOST
              value: "postgresql"
            - name: PGPORT
              value: "5432"
            - name: PGUSER
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: postgres-username
            - name: PGDATABASE
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: postgres-db
      containers:
        - name: migrate
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["./migrate"]
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-postgresql
                  key: database-url
          # Add liveness and readiness probes
          livenessProbe:
            exec:
              command: ["pg_isready", "-h", "warda-postgresql", "-U", "postgres"]
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
          readinessProbe:
            exec:
              command: ["pg_isready", "-h", "warda-postgresql", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
