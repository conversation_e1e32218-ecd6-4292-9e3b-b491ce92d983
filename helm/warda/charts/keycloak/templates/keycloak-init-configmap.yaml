apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "keycloak.fullname" . }}-init-script
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
data:
  init-keycloak-db.sh: |
    #!/bin/bash
    set -e
    
    echo "🔧 Initializing Keycloak database setup..."
    
    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    until pg_isready -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres; do
      echo "PostgreSQL not ready, waiting..."
      sleep 2
    done

    echo "✅ PostgreSQL is ready"

    # Check if keycloak database exists, create if not
    echo "🔍 Checking if keycloak database exists..."
    if ! psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -lqt | cut -d \| -f 1 | grep -qw {{ .Values.database.name }}; then
      echo "📦 Creating keycloak database..."
      psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "CREATE DATABASE {{ .Values.database.name }};"
      echo "✅ Keycloak database created"
    else
      echo "✅ Keycloak database already exists"
    fi

    # Check if keycloak user exists, create if not
    echo "🔍 Checking if keycloak user exists..."
    if ! psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -t -c "SELECT 1 FROM pg_roles WHERE rolname='{{ .Values.database.username }}'" | grep -q 1; then
      echo "👤 Creating keycloak user..."
      psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "CREATE USER {{ .Values.database.username }} WITH PASSWORD '{{ .Values.database.password }}';"
      echo "✅ Keycloak user created"
    else
      echo "✅ Keycloak user already exists"
    fi

    # Grant privileges
    echo "🔐 Granting privileges to keycloak user..."
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE {{ .Values.database.name }} TO {{ .Values.database.username }};"
    psql -h {{ .Values.database.host }} -p {{ .Values.database.port }} -U postgres -c "GRANT CONNECT ON DATABASE {{ .Values.database.name }} TO {{ .Values.database.username }};"
    
    echo "✅ Keycloak database initialization complete"
