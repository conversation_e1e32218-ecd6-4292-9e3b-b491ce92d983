apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "keycloak.fullname" . }}-realm-config
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
data:
  warda-realm.json: |
    {
      "realm": "{{ .Values.realm.name }}",
      "displayName": "{{ .Values.realm.displayName }}",
      "enabled": {{ .Values.realm.enabled }},
      "sslRequired": "none",
      "registrationAllowed": true,
      "loginWithEmailAllowed": true,
      "duplicateEmailsAllowed": false,
      "resetPasswordAllowed": true,
      "editUsernameAllowed": true,
      "bruteForceProtected": true,
      "clients": [
        {
          "clientId": "{{ .Values.realm.frontendClient.clientId }}",
          "name": "{{ .Values.realm.frontendClient.name }}",
          "enabled": {{ .Values.realm.frontendClient.enabled }},
          "publicClient": {{ .Values.realm.frontendClient.publicClient }},
          "directAccessGrantsEnabled": {{ .Values.realm.frontendClient.directAccessGrantsEnabled }},
          "standardFlowEnabled": {{ .Values.realm.frontendClient.standardFlowEnabled }},
          "implicitFlowEnabled": {{ .Values.realm.frontendClient.implicitFlowEnabled }},
          "serviceAccountsEnabled": {{ .Values.realm.frontendClient.serviceAccountsEnabled }},
          "redirectUris": {{ .Values.realm.frontendClient.redirectUris | toJson }},
          "webOrigins": {{ .Values.realm.frontendClient.webOrigins | toJson }},
          "protocol": "openid-connect",
          "attributes": {
            "post.logout.redirect.uris": "+"
          }
        },
        {
          "clientId": "{{ .Values.realm.backendClient.clientId }}",
          "name": "{{ .Values.realm.backendClient.name }}",
          "enabled": {{ .Values.realm.backendClient.enabled }},
          "publicClient": {{ .Values.realm.backendClient.publicClient }},
          "directAccessGrantsEnabled": {{ .Values.realm.backendClient.directAccessGrantsEnabled }},
          "standardFlowEnabled": {{ .Values.realm.backendClient.standardFlowEnabled }},
          "serviceAccountsEnabled": {{ .Values.realm.backendClient.serviceAccountsEnabled }},
          "secret": "{{ .Values.realm.backendClient.secret }}",
          "redirectUris": {{ .Values.realm.backendClient.redirectUris | toJson }},
          "protocol": "openid-connect",
          "bearerOnly": false,
          "consentRequired": false,
          "frontchannelLogout": true
        }
      ],
      "roles": {
        "realm": [
          {
            "name": "user",
            "description": "User role"
          },
          {
            "name": "admin",
            "description": "Administrator role"
          }
        ]
      },
      "users": [
        {
          "username": "testuser",
          "enabled": true,
          "email": "<EMAIL>",
          "firstName": "Test",
          "lastName": "User",
          "credentials": [
            {
              "type": "password",
              "value": "password",
              "temporary": false
            }
          ],
          "realmRoles": ["user"]
        }
      ]
    }
