apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" . }}-secrets
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
stringData:
  # Admin credentials
  KEYCLOAK_ADMIN: {{ .Values.keycloak.admin.username | default "admin" | quote }}
  KEYCLOAK_ADMIN_PASSWORD: {{ .Values.keycloak.admin.password | default "admin123" | quote }}
  
  # Database configuration with safe defaults
  {{- $dbConfig := dict }}
  {{- if .Values.keycloak.env }}
    {{- $dbConfig = merge $dbConfig .Values.keycloak.env }}
  {{- end }}
  
  KC_DB_USERNAME: {{ get $dbConfig "KC_DB_USERNAME" | default "keycloak" | quote }}
  KC_DB_PASSWORD: {{ get $dbConfig "KC_DB_PASSWORD" | default "keycloak_password" | quote }}
  KC_DB_URL: {{ get $dbConfig "KC_DB_URL" | default "************************************************" | quote }}
  KC_DB: {{ get $dbConfig "KC_DB" | default "postgres" | quote }}
  KC_DB_SCHEMA: {{ get $dbConfig "KC_DB_SCHEMA" | default "public" | quote }}
  KC_DB_DRIVER: {{ get $dbConfig "KC_DB_DRIVER" | default "postgres" | quote }}
  KC_TRANSACTION_XA_ENABLED: {{ get $dbConfig "KC_TRANSACTION_XA_ENABLED" | default "false" | quote }}
  
  # Host configuration
  KC_HOSTNAME: {{ get $dbConfig "KC_HOSTNAME" | default "keycloak.local" | quote }}
  KC_HOSTNAME_STRICT: {{ get $dbConfig "KC_HOSTNAME_STRICT" | default "false" | quote }}
  KC_HTTP_ENABLED: {{ get $dbConfig "KC_HTTP_ENABLED" | default "true" | quote }}
  
  # Proxy settings
  KC_PROXY: {{ get $dbConfig "KC_PROXY" | default "edge" | quote }}
  
  # Cache configuration
  KC_CACHE: {{ get $dbConfig "KC_CACHE" | default "local" | quote }}
  KC_CACHE_CONFIG_FILE: {{ get $dbConfig "KC_CACHE_CONFIG_FILE" | default "cache-ispn.xml" | quote }}
  
  # JVM Options (optional)
  {{- if hasKey $dbConfig "JAVA_OPTS" }}
  JAVA_OPTS: {{ get $dbConfig "JAVA_OPTS" | quote }}
  {{- end }}
  
  # Backend client secret with safe defaults
  {{- $realm := default dict .Values.keycloak.realm }}
  {{- $backendClient := default dict $realm.backendClient }}
  BACKEND_CLIENT_SECRET: {{ default "backend-client-secret" $backendClient.secret | quote }}
