apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" . }}-secrets
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
stringData:
  KEYCLOAK_ADMIN: {{ .Values.keycloak.admin.username | quote }}
  KEYCLOAK_ADMIN_PASSWORD: {{ .Values.keycloak.admin.password | quote }}
  KC_DB_USERNAME: {{ .Values.keycloak.database.username | quote }}
  KC_DB_PASSWORD: {{ .Values.keycloak.database.password | quote }}
  KC_DB_URL_DATABASE: {{ .Values.keycloak.database.database | quote }}
  BACKEND_CLIENT_SECRET: {{ .Values.realm.backendClient.secret | quote }}
