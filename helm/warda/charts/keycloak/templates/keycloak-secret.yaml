apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" . }}-secrets
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
type: Opaque
stringData:
  # Admin credentials
  KEYCLOAK_ADMIN: {{ .Values.admin.username | quote }}
  KEYCLOAK_ADMIN_PASSWORD: {{ .Values.admin.password | quote }}

  # Database credentials
  KC_DB_USERNAME: {{ .Values.database.username | quote }}
  KC_DB_PASSWORD: {{ .Values.database.password | quote }}
  KC_DB_URL_DATABASE: {{ .Values.database.name | quote }}

  # Backend client secret
  BACKEND_CLIENT_SECRET: {{ .Values.realm.backendClient.secret | quote }}
