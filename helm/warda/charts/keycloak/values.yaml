# Default values for keycloak.
# This is a YAML-formatted file.

# Replica count
replicaCount: 1

# Image configuration
image:
  repository: quay.io/keycloak/keycloak
  tag: "23.0.0"
  pullPolicy: IfNotPresent

# Keycloak configuration
keycloak:
  # Admin user configuration
  admin:
    username: admin
    password: admin123
  
  # Database configuration
  database:
    vendor: postgres
    host: postgresql
    port: 5432
    database: keycloak
    username: keycloak
    password: keycloak_password
    
  # Hostname configuration
  hostname:
    hostname: keycloak.local
    strict: false
    
  # HTTP configuration
  http:
    enabled: true
    port: 8080
    
  # Proxy configuration for development
  proxy:
    edge: true

# Environment variables
env:
  KC_DB: postgres
  KC_DB_URL_HOST: postgresql
  KC_DB_URL_PORT: "5432"
  KC_DB_URL_DATABASE: keycloak
  KC_DB_USERNAME: keycloak
  KC_DB_PASSWORD: keycloak_password
  KC_HOSTNAME: keycloak.local
  KC_HOSTNAME_STRICT: "false"
  KC_HTTP_ENABLED: "true"
  KC_PROXY: edge
  KEYCLOAK_ADMIN: admin
  K<PERSON>OAK_ADMIN_PASSWORD: admin123

# Service configuration
service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

# Ingress configuration
ingress:
  enabled: true
  className: ""
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
  hosts:
    - host: keycloak.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

# Readiness and liveness probes
readinessProbe:
  httpGet:
    path: /realms/master
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

livenessProbe:
  httpGet:
    path: /realms/master
    port: 8080
  initialDelaySeconds: 120
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

# Pod configuration
podAnnotations: {}
podLabels: {}

# Security context
podSecurityContext: {}
securityContext: {}

# Node selector, affinity, and tolerations
nodeSelector: {}
affinity: {}
tolerations: []

# Realm and client configuration
realm:
  name: warda
  displayName: "Warda Platform"
  enabled: true
  
  # Frontend client (public client for SPA)
  frontendClient:
    clientId: warda-frontend
    name: "Warda Frontend"
    enabled: true
    publicClient: true
    directAccessGrantsEnabled: false
    standardFlowEnabled: true
    implicitFlowEnabled: false
    serviceAccountsEnabled: false
    redirectUris:
      - "http://localhost:3000/*"
      - "http://frontend.local/*"
    webOrigins:
      - "http://localhost:3000"
      - "http://frontend.local"
    
  # Backend client (confidential client for API)
  backendClient:
    clientId: warda-backend
    name: "Warda Backend"
    enabled: true
    publicClient: false
    directAccessGrantsEnabled: true
    standardFlowEnabled: true
    serviceAccountsEnabled: true
    secret: backend-client-secret
    redirectUris:
      - "http://localhost:8080/*"
      - "http://backend.local/*"
