# Default values for keycloak.
# This is a YAML-formatted file.
#
# Note: Most configuration has been moved to the parent chart's values.yaml
# This file is kept for documentation and to maintain the chart structure.
# Any values defined here can be overridden from the parent chart.

# Replica count
replicaCount: 1

# Minimal image configuration (can be overridden from parent)
image:
  repository: quay.io/keycloak/keycloak
  tag: "23.0.0"
  pullPolicy: IfNotPresent

# Keycloak configuration
# Most configuration should be done in the parent chart's values.yaml
keycloak:
  enabled: true
  
  # Admin user configuration (can be overridden from parent)
  admin:
    username: admin
    password: admin123
    
  # Hostname configuration (can be overridden from parent)
  hostname:
    hostname: keycloak.local
    strict: false

# Service configuration (can be overridden from parent)
service:
  type: ClusterIP
  port: 80
  targetPort: 8080
  timeoutSeconds: 5
  failureThreshold: 3

# Pod configuration
podAnnotations: {}
podLabels: {}

# Security context
podSecurityContext: {}
securityContext: {}

# Node selector, affinity, and tolerations
nodeSelector: {}
affinity: {}
tolerations: []

# Realm and client configuration
realm:
  name: warda
  displayName: "Warda Platform"
  enabled: true
  
  # Frontend client (public client for SPA)
  frontendClient:
    clientId: warda-frontend
    name: "Warda Frontend"
    enabled: true
    publicClient: true
    directAccessGrantsEnabled: false
    standardFlowEnabled: true
    implicitFlowEnabled: false
    serviceAccountsEnabled: false
    redirectUris:
      - "http://localhost:3000/*"
      - "http://frontend.local/*"
    webOrigins:
      - "http://localhost:3000"
      - "http://frontend.local"
    
  # Backend client (confidential client for API)
  backendClient:
    clientId: warda-backend
    name: "Warda Backend"
    enabled: true
    publicClient: false
    directAccessGrantsEnabled: true
    standardFlowEnabled: true
    serviceAccountsEnabled: true
    secret: backend-client-secret
    redirectUris:
      - "http://localhost:8080/*"
      - "http://backend.local/*"
