apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-postgresql
  labels:
    app.kubernetes.io/name: postgresql
    helm.sh/chart: postgresql-0.1.0
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    app.kubernetes.io/component: database
type: Opaque
stringData:
  postgres-password: {{ .Values.postgresqlPassword | quote }}
  postgres-username: {{ .Values.postgresqlUsername | quote }}
  postgres-db: {{ .Values.postgresqlDatabase | quote }}
  database-url: postgresql://{{ .Values.postgresqlUsername }}:{{ .Values.postgresqlPassword }}@postgresql:{{ .Values.service.port }}/{{ .Values.postgresqlDatabase }}
  DATABASE_URL: postgresql://{{ .Values.postgresqlUsername }}:{{ .Values.postgresqlPassword }}@postgresql:{{ .Values.service.port }}/{{ .Values.postgresqlDatabase }}
