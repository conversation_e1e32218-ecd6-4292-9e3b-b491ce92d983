dependencies:
- name: "loki"
  condition: loki.enabled
  repository: "https://grafana.github.io/helm-charts"
  version: "^2.15.2"
- name: "promtail"
  condition: promtail.enabled
  repository: "https://grafana.github.io/helm-charts"
  version: "^6.7.4"
- name: "fluent-bit"
  condition: fluent-bit.enabled
  repository: "https://grafana.github.io/helm-charts"
  version: "^2.3.0"
- name: "grafana"
  condition: grafana.enabled
  version: "~6.43.0"
  repository:  "https://grafana.github.io/helm-charts"
- name: "prometheus"
  condition: prometheus.enabled
  version: "~19.7.2"
  repository:  "https://prometheus-community.github.io/helm-charts"
- name: "filebeat"
  condition: filebeat.enabled
  version: "~7.17.1"
  repository:  "https://helm.elastic.co"
- name: "logstash"
  condition: logstash.enabled
  version: "~7.17.1"
  repository:  "https://helm.elastic.co"
