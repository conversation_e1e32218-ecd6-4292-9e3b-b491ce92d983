{{- if (and .Values.test_pod.enabled .Values.loki.enabled) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "loki-stack.fullname" . }}-test
  labels:
    app: {{ template "loki-stack.name" . }}
    chart: {{ template "loki-stack.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
data:
  test.sh: |
    #!/usr/bin/env bash

    LOKI_URI="http://${LOKI_SERVICE}:${LOKI_PORT}"

    function setup() {
      apk add -u curl jq
      until (curl -s ${LOKI_URI}/loki/api/v1/label/app/values | jq -e '.data[] | select(. == "loki")'); do
        sleep 1
      done
    }

    @test "Has labels" {
      curl -s ${LOKI_URI}/loki/api/v1/labels | \
      jq -e '.data[] | select(. == "app")'
    }

    @test "Query log entry" {
      curl -sG ${LOKI_URI}/api/prom/query?limit=10 --data-urlencode 'query={app="loki"}' | \
      jq -e '.streams[].entries | length >=1'
    }

    @test "Push log entry" {
      local timestamp=$(date +%s000000000)
      local data=$(jq -n --arg timestamp "${timestamp}" '{"streams": [{"stream": {"app": "loki-test"}, "values": [[$timestamp, "foobar"]]}]}')

      curl -s -X POST -H "Content-Type: application/json" ${LOKI_URI}/loki/api/v1/push --data-raw "${data}"

      curl -sG ${LOKI_URI}/loki/api/v1/query_range?limit=1 --data-urlencode 'query={app="loki-test"}' | \
      jq -e '.data.result[].values[][1] == "foobar"'
    }
{{- end }}
