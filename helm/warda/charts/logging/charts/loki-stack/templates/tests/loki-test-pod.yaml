{{- if (and .Values.test_pod.enabled .Values.loki.enabled) }}
apiVersion: v1
kind: Pod
metadata:
  annotations:
    "helm.sh/hook": test-success
  labels:
    app: {{ template "loki-stack.name" . }}
    chart: {{ template "loki-stack.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  name: {{ template "loki-stack.fullname" . }}-test
spec:
  containers:
  - name: test
    image: "{{ .Values.test_pod.image }}"
    imagePullPolicy: "{{ .Values.test_pod.imagePullPolicy}}"
    args:
    - /var/lib/loki/test.sh
    env:
    - name: LOKI_SERVICE
      value: {{ template "loki.serviceName" . }}
    - name: LOKI_PORT
      value: "{{ .Values.loki.service.port }}"
    {{- with .Values.proxy.http_proxy }}
    - name: HTTP_PROXY
      value: "{{ . }}"
    {{- end }}
    {{- with .Values.proxy.https_proxy }}
    - name: HTTPS_PROXY
      value: "{{ . }}"
    {{- end }}
    {{- with .Values.proxy.no_proxy }}
    - name: NO_PROXY
      value: "{{ . }}"
    {{- end }}
    volumeMounts:
    - name: tests
      mountPath: /var/lib/loki
  restartPolicy: Never
  volumes:
  - name: tests
    configMap:
      name: {{ template "loki-stack.fullname" . }}-test
{{- end }}
