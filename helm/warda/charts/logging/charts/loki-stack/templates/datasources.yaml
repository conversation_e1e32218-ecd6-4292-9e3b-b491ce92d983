{{- if .Values.grafana.sidecar.datasources.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "loki-stack.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "loki-stack.name" . }}
    chart: {{ template "loki-stack.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
    {{- if .Values.grafana.sidecar.datasources.label }}
    {{ .Values.grafana.sidecar.datasources.label }}: {{ .Values.grafana.sidecar.datasources.labelValue | quote }}
    {{- else }}
    grafana_datasource: "1"
    {{- end }}
data:
  loki-stack-datasource.yaml: |-
    apiVersion: 1
    datasources:
{{- if .Values.loki.enabled }}
    - name: Loki
      type: loki
      access: proxy
      url: {{ tpl .Values.loki.url . | quote }}
      version: 1
      isDefault: {{ default false .Values.loki.isDefault }}
      {{- with .Values.loki.datasource.uid }}
      uid: {{ . | quote }}
      {{- end }}
      {{- with .Values.loki.datasource.jsonData }}
      jsonData:
        {{- tpl . $ | nindent 8 }}
      {{- end }}
{{- end }}

{{- if .Values.prometheus.enabled }}
    - name: Prometheus
      type: prometheus
      access: proxy
      url: {{ tpl .Values.prometheus.url . | quote }}
      version: 1
      isDefault: {{ default false .Values.prometheus.isDefault }}
      {{- with .Values.prometheus.datasource.jsonData }}
      jsonData:
        {{- tpl . $ | nindent 8 }}
      {{- end }}
{{- end }}
{{- end }}
