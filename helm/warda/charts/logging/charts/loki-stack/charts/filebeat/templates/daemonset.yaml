{{- if .Values.daemonset.enabled }}
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ template "filebeat.fullname" . }}
  labels:
    app: "{{ template "filebeat.fullname" . }}"
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    {{- if .Values.daemonset.labels }}
    {{- range $key, $value := .Values.daemonset.labels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- else }}
    {{- range $key, $value := .Values.labels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
    {{- end }}
  {{- if .Values.daemonset.annotations }}
  annotations:
    {{- range $key, $value := .Values.daemonset.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  selector:
    matchLabels:
      app: "{{ template "filebeat.fullname" . }}"
      release: {{ .Release.Name | quote }}
  updateStrategy:
    {{- if eq .Values.updateStrategy "RollingUpdate" }}
    rollingUpdate:
      maxUnavailable: {{ .Values.daemonset.maxUnavailable }}
    {{- end }}
    type: {{ .Values.updateStrategy }}
  template:
    metadata:
      annotations:
        {{- range $key, $value := .Values.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{/* This forces a restart if the configmap has changed */}}
        {{- if or  .Values.filebeatConfig .Values.daemonset.filebeatConfig }}
        configChecksum: {{ include (print .Template.BasePath "/configmap.yaml") . | sha256sum | trunc 63 }}
        {{- end }}
      name: "{{ template "filebeat.fullname" . }}"
      labels:
        app: "{{ template "filebeat.fullname" . }}"
        chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
        heritage: {{ .Release.Service | quote }}
        release: {{ .Release.Name | quote }}
        {{- if .Values.daemonset.labels }}
        {{- range $key, $value := .Values.daemonset.labels }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- else }}
        {{- range $key, $value := .Values.labels }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- end }}
    spec:
      tolerations: {{ toYaml ( .Values.tolerations | default .Values.daemonset.tolerations ) | nindent 8 }}
      nodeSelector: {{ toYaml ( .Values.nodeSelector | default .Values.daemonset.nodeSelector ) | nindent 8 }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName  }}
      {{- end }}
      affinity: {{ toYaml ( .Values.affinity | default .Values.daemonset.affinity ) | nindent 8 }}
      serviceAccountName: {{ template "filebeat.serviceAccount" . }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriod }}
      {{- if .Values.daemonset.hostNetworking }}
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      {{- end }}
      {{- if .Values.dnsConfig }}
      dnsConfig: {{ toYaml .Values.dnsConfig | nindent 8 }}
      {{- end }}
      {{- if .Values.hostAliases | default .Values.daemonset.hostAliases }}
      hostAliases: {{ toYaml ( .Values.hostAliases | default .Values.daemonset.hostAliases ) | nindent 8 }}
      {{- end }}
      volumes:
      {{- range .Values.secretMounts | default .Values.daemonset.secretMounts }}
      - name: {{ .name }}
        secret:
          secretName: {{ .secretName }}
      {{- end }}
      {{- if .Values.filebeatConfig }}
      - name: filebeat-config
        configMap:
          defaultMode: 0600
          name: {{ template "filebeat.fullname" . }}-config
      {{- else if .Values.daemonset.filebeatConfig }}
      - name: filebeat-config
        configMap:
          defaultMode: 0600
          name: {{ template "filebeat.fullname" . }}-daemonset-config
      {{- end }}
      - name: data
        hostPath:
          path: {{ .Values.hostPathRoot }}/{{ template "filebeat.fullname" . }}-{{ .Release.Namespace }}-data
          type: DirectoryOrCreate
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: varlog
        hostPath:
          path: /var/log
      - name: varrundockersock
        hostPath:
          path: /var/run/docker.sock
      {{- if .Values.extraVolumes | default .Values.daemonset.extraVolumes }}
{{ toYaml ( .Values.extraVolumes | default .Values.daemonset.extraVolumes )  | indent 6 }}
      {{- end }}
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml .Values.imagePullSecrets | indent 8 }}
      {{- end }}
      {{- if .Values.extraInitContainers }}
      initContainers:
      # All the other beats accept a string here while
      # filebeat accepts a valid yaml array. We're keeping
      # this as a backwards compatible change, while adding
      # also a way to pass a string as other templates to
      # make these implementations consistent.
      # https://github.com/elastic/helm-charts/issues/490
      {{- if eq "string" (printf "%T" .Values.extraInitContainers) }}
{{ tpl .Values.extraInitContainers . | indent 8 }}
      {{- else }}
{{ toYaml .Values.extraInitContainers | indent 8 }}
      {{- end }}
      {{- end }}
      containers:
      - name: "filebeat"
        image: "{{ .Values.image }}:{{ .Values.imageTag }}"
        imagePullPolicy: "{{ .Values.imagePullPolicy }}"
        args:
        - "-e"
        - "-E"
        - "http.enabled=true"
        livenessProbe:
{{ toYaml .Values.livenessProbe | indent 10 }}
        readinessProbe:
{{ toYaml .Values.readinessProbe | indent 10 }}
        resources:
{{ toYaml ( .Values.resources | default .Values.daemonset.resources )  | indent 10 }}
        env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
{{- if .Values.extraEnvs | default .Values.daemonset.extraEnvs }}
{{ toYaml ( .Values.extraEnvs | default .Values.daemonset.extraEnvs ) | indent 8 }}
{{- end }}
        envFrom: {{ toYaml ( .Values.envFrom | default .Values.daemonset.envFrom ) | nindent 10 }}
        securityContext: {{ toYaml ( .Values.podSecurityContext | default .Values.daemonset.securityContext ) | nindent 10 }}
        volumeMounts:
        {{- range .Values.secretMounts | default .Values.daemonset.secretMounts }}
        - name: {{ .name }}
          mountPath: {{ .path }}
          {{- if .subPath }}
          subPath: {{ .subPath }}
          {{- end }}
        {{- end }}
        {{- range $path, $config := .Values.filebeatConfig }}
        - name: filebeat-config
          mountPath: /usr/share/filebeat/{{ $path }}
          readOnly: true
          subPath: {{ $path }}
        {{ else }}
        {{- range $path, $config := .Values.daemonset.filebeatConfig }}
        - name: filebeat-config
          mountPath: /usr/share/filebeat/{{ $path }}
          readOnly: true
          subPath: {{ $path }}
        {{- end }}
        {{- end }}
        - name: data
          mountPath: /usr/share/filebeat/data
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: varlog
          mountPath: /var/log
          readOnly: true
        # Necessary when using autodiscovery; avoid mounting it otherwise
        # See: https://www.elastic.co/guide/en/beats/filebeat/7.17/configuration-autodiscover.html
        - name: varrundockersock
          mountPath: /var/run/docker.sock
          readOnly: true
        {{- if .Values.extraVolumeMounts | default .Values.daemonset.extraVolumeMounts }}
{{ toYaml (.Values.extraVolumeMounts | default .Values.daemonset.extraVolumeMounts ) | indent 8 }}
        {{- end }}
      {{- if .Values.extraContainers }}
{{ tpl .Values.extraContainers . | indent 6 }}
      {{- end }}
{{- end }}
