---
daemonset:
  # Annotations to apply to the daemonset
  annotations: {}
  # additionals labels
  labels: {}
  affinity: {}
  # Include the daemonset
  enabled: true
  # Extra environment variables for Filebeat container.
  envFrom: []
  # - configMapRef:
  #     name: config-secret
  extraEnvs: []
  #  - name: MY_ENVIRONMENT_VAR
  #    value: the_value_goes_here
  extraVolumes:
    []
    # - name: extras
    #   emptyDir: {}
  extraVolumeMounts:
    []
    # - name: extras
    #   mountPath: /usr/share/extras
    #   readOnly: true
  hostNetworking: false
  # Allows you to add any config files in /usr/share/filebeat
  # such as filebeat.yml for daemonset
  filebeatConfig:
    filebeat.yml: |
      filebeat.inputs:
      - type: container
        paths:
          - /var/log/containers/*.log
        processors:
        - add_kubernetes_metadata:
            host: ${NODE_NAME}
            matchers:
            - logs_path:
                logs_path: "/var/log/containers/"

      output.elasticsearch:
        host: '${NODE_NAME}'
        hosts: '${ELASTICSEARCH_HOSTS:elasticsearch-master:9200}'
  # Only used when updateStrategy is set to "RollingUpdate"
  maxUnavailable: 1
  nodeSelector: {}
  # A list of secrets and their paths to mount inside the pod
  # This is useful for mounting certificates for security other sensitive values
  secretMounts: []
  #  - name: filebeat-certificates
  #    secretName: filebeat-certificates
  #    path: /usr/share/filebeat/certs
  # Various pod security context settings. Bear in mind that many of these have an impact on Filebeat functioning properly.
  #
  # - User that the container will execute as. Typically necessary to run as root (0) in order to properly collect host container logs.
  # - Whether to execute the Filebeat containers as privileged containers. Typically not necessarily unless running within environments such as OpenShift.
  securityContext:
    runAsUser: 0
    privileged: false
  resources:
    requests:
      cpu: "100m"
      memory: "100Mi"
    limits:
      cpu: "1000m"
      memory: "200Mi"
  tolerations: []

deployment:
  # Annotations to apply to the deployment
  annotations: {}
  # additionals labels
  labels: {}
  affinity: {}
  # Include the deployment
  enabled: false
  # Extra environment variables for Filebeat container.
  envFrom: []
  # - configMapRef:
  #     name: config-secret
  extraEnvs: []
  #  - name: MY_ENVIRONMENT_VAR
  #    value: the_value_goes_here
  # Allows you to add any config files in /usr/share/filebeat
  extraVolumes: []
  # - name: extras
  #   emptyDir: {}
  extraVolumeMounts: []
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true
  # such as filebeat.yml for deployment
  filebeatConfig:
    filebeat.yml: |
      filebeat.inputs:
      - type: tcp
        max_message_size: 10MiB
        host: "localhost:9000"

      output.elasticsearch:
        host: '${NODE_NAME}'
        hosts: '${ELASTICSEARCH_HOSTS:elasticsearch-master:9200}'
  nodeSelector: {}
  # A list of secrets and their paths to mount inside the pod
  # This is useful for mounting certificates for security other sensitive values
  secretMounts: []
  #  - name: filebeat-certificates
  #    secretName: filebeat-certificates
  #    path: /usr/share/filebeat/certs
  #
  # - User that the container will execute as.
  # Not necessary to run as root (0) as the Filebeat Deployment use cases do not need access to Kubernetes Node internals
  # - Typically not necessarily unless running within environments such as OpenShift.
  securityContext:
    runAsUser: 0
    privileged: false
  resources:
    requests:
      cpu: "100m"
      memory: "100Mi"
    limits:
      cpu: "1000m"
      memory: "200Mi"
  tolerations: []

# Replicas being used for the filebeat deployment
replicas: 1

extraContainers: ""
# - name: dummy-init
#   image: busybox
#   command: ['echo', 'hey']

extraInitContainers: []
# - name: dummy-init

# Root directory where Filebeat will write data to in order to persist registry data across pod restarts (file position and other metadata).
hostPathRoot: /var/lib

dnsConfig: {}
# options:
#   - name: ndots
#     value: "2"
hostAliases: []
#- ip: "127.0.0.1"
#  hostnames:
#  - "foo.local"
#  - "bar.local"
image: "docker.elastic.co/beats/filebeat"
imageTag: "7.17.3"
imagePullPolicy: "IfNotPresent"
imagePullSecrets: []

livenessProbe:
  exec:
    command:
      - sh
      - -c
      - |
        #!/usr/bin/env bash -e
        curl --fail 127.0.0.1:5066
  failureThreshold: 3
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5

readinessProbe:
  exec:
    command:
      - sh
      - -c
      - |
        #!/usr/bin/env bash -e
        filebeat test output
  failureThreshold: 3
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5

# Whether this chart should self-manage its service account, role, and associated role binding.
managedServiceAccount: true

clusterRoleRules:
  - apiGroups:
      - ""
    resources:
      - namespaces
      - nodes
      - pods
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "apps"
    resources:
      - replicasets
    verbs:
      - get
      - list
      - watch

podAnnotations:
  {}
  # iam.amazonaws.com/role: es-cluster

# Custom service account override that the pod will use
serviceAccount: ""

# Annotations to add to the ServiceAccount that is created if the serviceAccount value isn't set.
serviceAccountAnnotations:
  {}
  # eks.amazonaws.com/role-arn: arn:aws:iam::************:role/k8s.clustername.namespace.serviceaccount

# How long to wait for Filebeat pods to stop gracefully
terminationGracePeriod: 30
# This is the PriorityClass settings as defined in
# https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass
priorityClassName: ""

updateStrategy: RollingUpdate

# Override various naming aspects of this chart
# Only edit these if you know what you're doing
nameOverride: ""
fullnameOverride: ""

# DEPRECATED
affinity: {}
envFrom: []
extraEnvs: []
extraVolumes: []
extraVolumeMounts: []
# Allows you to add any config files in /usr/share/filebeat
# such as filebeat.yml for both daemonset and deployment
filebeatConfig: {}
nodeSelector: {}
podSecurityContext: {}
resources: {}
secretMounts: []
tolerations: []
labels: {}
