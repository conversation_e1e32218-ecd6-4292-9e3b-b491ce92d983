{{- if .Values.managedServiceAccount }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "filebeat.serviceAccount" . }}
  annotations:
    {{- with .Values.serviceAccountAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    app: "{{ template "filebeat.fullname" . }}"
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
{{- end -}}
