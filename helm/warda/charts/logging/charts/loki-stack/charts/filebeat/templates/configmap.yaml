{{- if .Values.filebeatConfig }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "filebeat.fullname" . }}-config
  labels:
    app: "{{ template "filebeat.fullname" . }}"
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
data:
{{- range $path, $config := .Values.filebeatConfig }}
  {{ $path }}: |
{{ $config | indent 4 -}}
{{- end -}}
{{- end -}}

{{- if and .Values.daemonset.enabled .Values.daemonset.filebeatConfig }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "filebeat.fullname" . }}-daemonset-config
  labels:
    app: "{{ template "filebeat.fullname" . }}"
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
data:
{{- range $path, $config := .Values.daemonset.filebeatConfig }}
  {{ $path }}: |
{{ $config | indent 4 -}}
{{- end -}}
{{- end -}}

{{- if and .Values.deployment.enabled .Values.deployment.filebeatConfig }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "filebeat.fullname" . }}-deployment-config
  labels:
    app: "{{ template "filebeat.fullname" . }}"
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
data:
{{- range $path, $config := .Values.deployment.filebeatConfig }}
  {{ $path }}: |
{{ $config | indent 4 -}}
{{- end -}}
{{- end -}}
