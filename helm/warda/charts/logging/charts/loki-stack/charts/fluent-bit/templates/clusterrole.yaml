{{- if .Values.rbac.create }}
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  labels:
    app: {{ template "fluent-bit-loki.name" . }}
    chart: {{ template "fluent-bit-loki.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  name: {{ template "fluent-bit-loki.fullname" . }}-clusterrole
rules:
- apiGroups: [""] # "" indicates the core API group
  resources:
  - namespaces
  - pods
  verbs: ["get", "watch", "list"]
{{- end }}
