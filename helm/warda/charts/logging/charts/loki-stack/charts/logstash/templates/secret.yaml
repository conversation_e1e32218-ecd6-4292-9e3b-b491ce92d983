{{- if .Values.secrets }}
{{- $fullName := include "logstash.fullname" . -}}
{{- range .Values.secrets }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-%s" $fullName .name | quote }}
  labels:
    app: {{ $fullName | quote }}
    chart: {{ $.Chart.Name | quote }}
    heritage: {{ $.Release.Service | quote }}
    release: {{ $.Release.Name | quote }}
    {{- range $key, $value := $.Values.labels }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
data:
{{- range $key, $val := .value }}
  {{- if hasSuffix "filepath" $key }}
  {{ $key | replace ".filepath" "" }}: {{ $.Files.Get $val | b64enc | quote }}
  {{ else }}
  {{ $key }}: {{ $val | b64enc | quote }}
  {{- end }}
{{- end }}
type: Opaque
{{- end }}
{{- end }}