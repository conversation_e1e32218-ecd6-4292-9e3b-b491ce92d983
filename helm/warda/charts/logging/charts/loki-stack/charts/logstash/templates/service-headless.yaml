---
kind: Service
apiVersion: v1
metadata:
  name: "{{ template "logstash.fullname" . }}-headless"
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
{{- if .Values.labels }}
{{ toYaml .Values.labels | indent 4 }}
{{- end }}
spec:
  clusterIP: None
  selector:
    app: "{{ template "logstash.fullname" . }}"
  ports:
    - name: http
      port: {{ .Values.httpPort }}
