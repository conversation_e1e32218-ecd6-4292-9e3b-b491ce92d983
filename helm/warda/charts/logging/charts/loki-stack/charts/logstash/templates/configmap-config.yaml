{{- if .Values.logstashConfig }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "logstash.fullname" . }}-config
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
data:
{{- range $path, $config := .Values.logstashConfig }}
  {{ $path }}: |
{{ tpl $config $ | indent 4 -}}
{{- end -}}
{{- end -}}
