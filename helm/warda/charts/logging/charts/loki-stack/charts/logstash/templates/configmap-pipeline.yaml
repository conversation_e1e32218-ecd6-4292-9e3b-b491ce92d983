{{- if .Values.logstashPipeline }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "logstash.fullname" . }}-pipeline
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
data:
{{- range $path, $config := .Values.logstashPipeline }}
  {{ $path }}: |
{{ tpl $config $ | indent 4 -}}
{{- end -}}
{{- end -}}
