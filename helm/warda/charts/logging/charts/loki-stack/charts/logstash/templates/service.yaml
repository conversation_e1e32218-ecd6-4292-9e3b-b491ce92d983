{{- if .Values.service }}
---
kind: Service
apiVersion: v1
metadata:
  name: "{{ template "logstash.fullname" . }}"
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
  annotations:
{{ toYaml .Values.service.annotations | indent 4 }}
spec:
  type: {{ .Values.service.type }}
{{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
{{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml . | indent 4 }}
{{- end }}
{{- if .Values.service.externalTrafficPolicy }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy }}
{{- end }}
  selector:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    release: {{ .Release.Name | quote }}
  ports:
{{ toYaml .Values.service.ports | indent 4 }}
{{- end }}
