{{- if .Values.rbac.create -}}
{{- $fullName := include "logstash.fullname" . -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ template "logstash.serviceAccount" . }}"
  annotations:
    {{- with .Values.rbac.serviceAccountAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
  {{- if .Values.rbac.annotations }}
  annotations:
    {{- range $key, $value := .Values.rbac.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
{{- end -}}
