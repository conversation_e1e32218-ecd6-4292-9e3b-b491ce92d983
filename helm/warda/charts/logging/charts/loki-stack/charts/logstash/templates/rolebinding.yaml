{{- if .Values.rbac.create -}}
{{- $fullName := include "logstash.fullname" . -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $fullName | quote }}
  labels:
    app: "{{ template "logstash.fullname" . }}"
    chart: "{{ .Chart.Name }}"
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
subjects:
  - kind: ServiceAccount
    name: "{{ template "logstash.serviceAccount" . }}"
    namespace: {{ .Release.Namespace | quote }}
roleRef:
  kind: Role
  name: {{ $fullName | quote }}
  apiGroup: rbac.authorization.k8s.io
{{- end -}}
