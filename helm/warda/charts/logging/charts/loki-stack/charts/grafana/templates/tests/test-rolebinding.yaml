{{- if and .Values.testFramework.enabled .Values.rbac.pspEnabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "grafana.fullname" . }}-test
  namespace: {{ template "grafana.namespace" . }}
  annotations:
    "helm.sh/hook": test-success
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "grafana.fullname" . }}-test
subjects:
- kind: ServiceAccount
  name: {{ template "grafana.serviceAccountNameTest" . }}
  namespace: {{ template "grafana.namespace" . }}
{{- end }}
