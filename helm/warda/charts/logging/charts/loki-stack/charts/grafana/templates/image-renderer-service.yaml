{{ if .Values.imageRenderer.enabled }}
{{ if .Values.imageRenderer.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "grafana.fullname" . }}-image-renderer
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.imageRenderer.labels" . | nindent 4 }}
{{- if .Values.imageRenderer.service.labels }}
{{ toYaml .Values.imageRenderer.service.labels | indent 4 }}
{{- end }}
{{- with .Values.imageRenderer.service.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  type: ClusterIP
  {{- if .Values.imageRenderer.service.clusterIP }}
  clusterIP: {{ .Values.imageRenderer.service.clusterIP }}
  {{end}}
  ports:
    - name: {{ .Values.imageRenderer.service.portName }}
      port: {{ .Values.imageRenderer.service.port }}
      protocol: TCP
      targetPort: {{ .Values.imageRenderer.service.targetPort }}
      {{- if .Values.imageRenderer.appProtocol }}
      appProtocol: {{ .Values.imageRenderer.appProtocol }}
      {{- end }}
  selector:
    {{- include "grafana.imageRenderer.selectorLabels" . | nindent 4 }}
{{ end }}
{{ end }}
