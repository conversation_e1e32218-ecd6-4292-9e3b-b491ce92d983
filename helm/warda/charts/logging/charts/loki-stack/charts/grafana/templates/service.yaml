{{ if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "grafana.fullname" . }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- if .Values.service.labels }}
{{ toYaml .Values.service.labels | indent 4 }}
{{- end }}
{{- $root := . }}
{{- with .Values.service.annotations }}
  annotations:
{{ tpl (toYaml . | indent 4) $root }}
{{- end }}
spec:
{{- if (or (eq .Values.service.type "ClusterIP") (empty .Values.service.type)) }}
  type: ClusterIP
  {{- if .Values.service.clusterIP }}
  clusterIP: {{ .Values.service.clusterIP }}
  {{end}}
{{- else if eq .Values.service.type "LoadBalancer" }}
  type: {{ .Values.service.type }}
  {{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml .Values.service.loadBalancerSourceRanges | indent 4 }}
  {{- end -}}
{{- else }}
  type: {{ .Values.service.type }}
{{- end }}
{{- if .Values.service.externalIPs }}
  externalIPs:
{{ toYaml .Values.service.externalIPs | indent 4 }}
{{- end }}
  ports:
    - name: {{ .Values.service.portName }}
      port: {{ .Values.service.port }}
      protocol: TCP
      targetPort: {{ .Values.service.targetPort }}
      {{- if .Values.service.appProtocol }}
      appProtocol: {{ .Values.service.appProtocol }}
      {{- end }}
      {{- if (and (eq .Values.service.type "NodePort") (not (empty .Values.service.nodePort))) }}
      nodePort: {{.Values.service.nodePort}}
      {{ end }}
      {{- if .Values.extraExposePorts }}
      {{- tpl (toYaml .Values.extraExposePorts) . | nindent 4 }}
      {{- end }}
  selector:
    {{- include "grafana.selectorLabels" . | nindent 4 }}
{{ end }}
