{{- if .Values.testFramework.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "grafana.fullname" . }}-test
  namespace: {{ template "grafana.namespace" . }}
  annotations:
    "helm.sh/hook": test-success
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
data:
  run.sh: |-
    @test "Test Health" {
      url="http://{{ template "grafana.fullname" . }}/api/health"

      code=$(wget --server-response --spider --timeout 90 --tries 10 ${url} 2>&1 | awk '/^  HTTP/{print $2}')
      [ "$code" == "200" ]
    }
{{- end }}
