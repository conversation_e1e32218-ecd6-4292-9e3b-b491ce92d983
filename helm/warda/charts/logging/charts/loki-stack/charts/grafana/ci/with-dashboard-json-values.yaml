dashboards:
  my-provider:
    my-awesome-dashboard:
      # An empty but valid dashboard
      json: |
        {
          "__inputs": [],
          "__requires": [
            {
              "type": "grafana",
              "id": "grafana",
              "name": "<PERSON><PERSON>",
              "version": "6.3.5"
            }
          ],
          "annotations": {
            "list": [
              {
                "builtIn": 1,
                "datasource": "-- Grafana --",
                "enable": true,
                "hide": true,
                "iconColor": "rgba(0, 211, 255, 1)",
                "name": "Annotations & Alerts",
                "type": "dashboard"
              }
            ]
          },
          "editable": true,
          "gnetId": null,
          "graphTooltip": 0,
          "id": null,
          "links": [],
          "panels": [],
          "schemaVersion": 19,
          "style": "dark",
          "tags": [],
          "templating": {
            "list": []
          },
          "time": {
            "from": "now-6h",
            "to": "now"
          },
          "timepicker": {
            "refresh_intervals": ["5s"]
          },
          "timezone": "",
          "title": "Dummy Dashboard",
          "uid": "IdcYQooWk",
          "version": 1
        }
      datasource: Prometheus
