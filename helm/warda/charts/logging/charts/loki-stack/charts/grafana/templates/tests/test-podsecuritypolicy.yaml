{{- if and .Values.testFramework.enabled .Values.rbac.pspEnabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ template "grafana.fullname" . }}-test
  annotations:
    "helm.sh/hook": test-success
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
spec:
  allowPrivilegeEscalation: true
  privileged: false
  hostNetwork: false
  hostIPC: false
  hostPID: false
  fsGroup:
    rule: RunAsAny
  seLinux:
    rule: RunAsAny
  supplementalGroups:
    rule: RunAsAny
  runAsUser:
    rule: RunAsAny
  volumes:
  - configMap
  - downwardAPI
  - emptyDir
  - projected
  - csi
  - secret
{{- end }}
{{- end }}
