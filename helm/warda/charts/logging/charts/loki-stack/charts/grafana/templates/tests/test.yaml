{{- if .Values.testFramework.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: {{ template "grafana.fullname" . }}-test
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
  namespace: {{ template "grafana.namespace" . }}
spec:
  serviceAccountName: {{ template "grafana.serviceAccountNameTest" . }}
  {{- if .Values.testFramework.securityContext }}
  securityContext: {{ toYaml .Values.testFramework.securityContext | nindent 4 }}
  {{- end }}
  {{- $root := . }}
  {{- if .Values.image.pullSecrets }}
  imagePullSecrets:
  {{- range .Values.image.pullSecrets }}
    - name: {{ tpl . $root }}
  {{- end}}
  {{- end }}
  {{- with .Values.nodeSelector }}
  nodeSelector:
{{ toYaml . | indent 4 }}
  {{- end }}
  {{- $root := . }}
  {{- with .Values.affinity }}
  affinity:
{{ tpl (toYaml .) $root | indent 4 }}
  {{- end }}
  {{- with .Values.tolerations }}
  tolerations:
{{ toYaml . | indent 4 }}
  {{- end }}
  containers:
    - name: {{ .Release.Name }}-test
      image: "{{ .Values.testFramework.image}}:{{ .Values.testFramework.tag }}"
      imagePullPolicy: "{{ .Values.testFramework.imagePullPolicy}}"
      command: ["/opt/bats/bin/bats", "-t", "/tests/run.sh"]
      volumeMounts:
        - mountPath: /tests
          name: tests
          readOnly: true
  volumes:
  - name: tests
    configMap:
      name: {{ template "grafana.fullname" . }}-test
  restartPolicy: Never
{{- end }}
