{{- if .Values.rbac.create -}}
apiVersion: {{ template "grafana.rbac.apiVersion" . }}
kind: RoleBinding
metadata:
  name: {{ template "grafana.fullname" . }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
{{- if (not .Values.rbac.useExistingRole) }}
  name: {{ template "grafana.fullname" . }}
{{- else }}
  name: {{ .Values.rbac.useExistingRole }}
{{- end }}
subjects:
- kind: ServiceAccount
  name: {{ template "grafana.serviceAccountName" . }}
  namespace: {{ template "grafana.namespace" . }}
{{- end -}}
