{{- if .Values.ingress.enabled -}}
{{- $ingressApiIsStable := eq (include "grafana.ingress.isStable" .) "true" -}}
{{- $ingressSupportsIngressClassName := eq (include "grafana.ingress.supportsIngressClassName" .) "true" -}}
{{- $ingressSupportsPathType := eq (include "grafana.ingress.supportsPathType" .) "true" -}}
{{- $fullName := include "grafana.fullname" . -}}
{{- $servicePort := .Values.service.port -}}
{{- $ingressPath := .Values.ingress.path -}}
{{- $ingressPathType := .Values.ingress.pathType -}}
{{- $extraPaths := .Values.ingress.extraPaths -}}
apiVersion: {{ include "grafana.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- if .Values.ingress.labels }}
{{ toYaml .Values.ingress.labels | indent 4 }}
{{- end }}
  {{- if .Values.ingress.annotations }}
  annotations:
    {{- range $key, $value := .Values.ingress.annotations }}
    {{ $key }}: {{ tpl $value $ | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- if and $ingressSupportsIngressClassName .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- end -}}
{{- if .Values.ingress.tls }}
  tls:
{{ tpl (toYaml .Values.ingress.tls) $ | indent 4 }}
{{- end }}
  rules:
  {{- if .Values.ingress.hosts  }}
  {{- range .Values.ingress.hosts }}
    - host: {{ tpl . $}}
      http:
        paths:
{{- if $extraPaths }}
{{ toYaml $extraPaths | indent 10 }}
{{- end }}
          - path: {{ $ingressPath }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ $ingressPathType }}
            {{- end }}
            backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
  {{- end }}
  {{- else }}
    - http:
        paths:
          - backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
            {{- if $ingressPath }}
            path: {{ $ingressPath }}
            {{- end }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ $ingressPathType }}
            {{- end }}
  {{- end -}}
{{- end }}
