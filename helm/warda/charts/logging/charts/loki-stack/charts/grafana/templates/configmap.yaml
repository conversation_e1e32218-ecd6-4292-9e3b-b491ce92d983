{{- if .Values.createConfigmap }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "grafana.fullname" . }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
data:
{{- if .Values.plugins }}
  plugins: {{ join "," .Values.plugins }}
{{- end }}
  grafana.ini: |
{{- range $elem, $elemVal := index .Values "grafana.ini" }}
    {{- if not (kindIs "map" $elemVal) }}
    {{- if kindIs "invalid" $elemVal }}
    {{ $elem }} =
    {{- else if kindIs "string" $elemVal }}
    {{ $elem }} = {{ tpl $elemVal $ }}
    {{- else }}
    {{ $elem }} = {{ $elemVal }}
    {{- end }}
    {{- end }}
{{- end }}
{{- range $key, $value := index .Values "grafana.ini" }}
    {{- if kindIs "map" $value }}
    [{{ $key }}]
    {{- range $elem, $elemVal := $value }}
    {{- if kindIs "invalid" $elemVal }}
    {{ $elem }} =
    {{- else if kindIs "string" $elemVal }}
    {{ $elem }} = {{ tpl $elemVal $ }}
    {{- else }}
    {{ $elem }} = {{ $elemVal }}
    {{- end }}
    {{- end }}
    {{- end }}
{{- end }}

{{- if .Values.datasources }}
{{ $root := . }}
  {{- range $key, $value := .Values.datasources }}
  {{ $key }}: |
{{ tpl (toYaml $value | indent 4) $root }}
  {{- end -}}
{{- end -}}

{{- if .Values.notifiers }}
  {{- range $key, $value := .Values.notifiers }}
  {{ $key }}: |
{{ toYaml $value | indent 4 }}
  {{- end -}}
{{- end -}}

{{- if .Values.alerting }}
{{ $root := . }}
  {{- range $key, $value := .Values.alerting }}
  {{ $key }}: |
{{ tpl (toYaml $value | indent 4) $root }}
  {{- end -}}
{{- end -}}

{{- if .Values.dashboardProviders }}
  {{- range $key, $value := .Values.dashboardProviders }}
  {{ $key }}: |
{{ toYaml $value | indent 4 }}
  {{- end -}}
{{- end -}}

{{- if .Values.dashboards  }}
  download_dashboards.sh: |
    #!/usr/bin/env sh
    set -euf
    {{- if .Values.dashboardProviders }}
      {{- range $key, $value := .Values.dashboardProviders }}
        {{- range $value.providers }}
    mkdir -p {{ .options.path }}
        {{- end }}
      {{- end }}
    {{- end }}
  {{ $dashboardProviders := .Values.dashboardProviders }}
  {{- range $provider, $dashboards := .Values.dashboards }}
    {{- range $key, $value := $dashboards }}
      {{- if (or (hasKey $value "gnetId") (hasKey $value "url")) }}
    curl -skf \
    --connect-timeout 60 \
    --max-time 60 \
      {{- if not $value.b64content }}
    -H "Accept: application/json" \
        {{- if $value.token }}
    -H "Authorization: token {{ $value.token }}" \
        {{- end }}
        {{- if $value.bearerToken }}
    -H "Authorization: Bearer {{ $value.bearerToken }}" \
        {{- end }}
        {{- if $value.gitlabToken }}
    -H "PRIVATE-TOKEN: {{ $value.gitlabToken }}" \
        {{- end }}
    -H "Content-Type: application/json;charset=UTF-8" \
      {{- end -}}
    {{- $dpPath := "" -}}
    {{- range $kd := (index $dashboardProviders "dashboardproviders.yaml").providers -}}
      {{- if eq $kd.name $provider -}}
      {{- $dpPath = $kd.options.path -}}
      {{- end -}}
    {{- end -}}
    {{- if $value.url }}
      "{{ $value.url }}" \
    {{- else }}
      "https://grafana.com/api/dashboards/{{ $value.gnetId }}/revisions/{{- if $value.revision -}}{{ $value.revision }}{{- else -}}1{{- end -}}/download" \
    {{- end -}}
    {{- if $value.datasource }}
      {{- if kindIs "string" $value.datasource }}
      | sed '/-- .* --/! s/"datasource":.*,/"datasource": "{{ $value.datasource }}",/g' \
      {{- end -}}
      {{- if kindIs "slice" $value.datasource -}}
        {{- range $value.datasource }}
          | sed '/-- .* --/! s/${{"{"}}{{ .name }}}/{{ .value }}/g' \
        {{- end -}}
      {{- end -}}
    {{- end -}}
    {{- if $value.b64content }}
      | base64 -d \
    {{- end }}
    > "{{- if $dpPath -}}{{ $dpPath }}{{- else -}}/var/lib/grafana/dashboards/{{ $provider }}{{- end -}}/{{ $key }}.json"
      {{ end }}
    {{- end -}}
  {{- end }}
{{- end }}
{{- end }}
