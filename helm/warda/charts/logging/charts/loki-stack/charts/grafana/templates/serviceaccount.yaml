{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.labels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- $root := . }}
{{- with .Values.serviceAccount.annotations }}
  annotations:
{{ tpl (toYaml . | indent 4) $root }}
{{- end }}
  name: {{ template "grafana.serviceAccountName" . }}
  namespace: {{ template "grafana.namespace" . }}
{{- end }}
