{{- if .Values.daemonset.enabled }}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "promtail.fullname" . }}
  namespace: {{ include "promtail.namespaceName" . }}
  labels:
    {{- include "promtail.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.revisionHistoryLimit }}
  revisionHistoryLimit: {{ .Values.revisionHistoryLimit }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "promtail.selectorLabels" . | nindent 6 }}
  updateStrategy:
    {{- toYaml .Values.updateStrategy | nindent 4 }}
  template:
    {{- include "promtail.podTemplate" . | nindent 4 }}
{{- end }}
