{{ range $key, $values := .Values.extraPorts }}
{{ if .ingress }}
{{ $ingressApiIsStable := eq (include "promtail.ingress.isStable" $ ) "true" }}
{{ $ingressSupportsIngressClassName := eq (include "promtail.ingress.supportsIngressClassName" $ ) "true" }}
{{ $ingressSupportsPathType := eq (include "promtail.ingress.supportsPathType" $ ) "true" }}
---
apiVersion: {{ include "promtail.ingress.apiVersion" $ }}
kind: Ingress
metadata:
  name: {{ include "promtail.fullname" $ }}-{{ $key | lower }}
  labels:
    {{- include "promtail.labels" $ | nindent 4 }}
  {{- with .ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and $ingressSupportsIngressClassName .ingress.ingressClassName }}
  ingressClassName: {{ .ingress.ingressClassName }}
  {{- end -}}
  {{- if .ingress.tls }}
  tls:
    {{- range .ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      {{- with .secretName }}
      secretName: {{ . }}
      {{- end }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .ingress.hosts }}
    - host: {{ . | quote }}
      http:
        paths:
          - path: {{ $values.ingress.path | default "/" }}
            {{- if $ingressSupportsPathType }}
            pathType: Prefix
            {{- end }}
            backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ include "promtail.fullname" $ }}-{{ $key | lower }}
                port:
                  {{- if $values.service }}
                  number: {{ $values.service.port }}
                  {{ else }}
                  number: {{ $values.containerPort }}
                  {{ end }}
              {{- else }}
              serviceName: {{ include "promtail.fullname" $ }}-{{ $key | lower }}
              {{- if $values.service }}
              servicePort: {{ $values.service.port }}
              {{ else }}
              number: {{ $values.containerPort }}
              {{ end }}
              {{- end }}
    {{- end }}
{{- end }}
{{- end }}
