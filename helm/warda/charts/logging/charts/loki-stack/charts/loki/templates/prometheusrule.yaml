{{- if and .Values.serviceMonitor.enabled .Values.serviceMonitor.prometheusRule.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ template "loki.fullname" . }}
{{- if .Values.serviceMonitor.prometheusRule.namespace }}
  namespace: {{ .Values.serviceMonitor.prometheusRule.namespace | quote }}
{{- end }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
  {{- if .Values.serviceMonitor.prometheusRule.additionalLabels }}
    {{- toYaml .Values.serviceMonitor.prometheusRule.additionalLabels | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.serviceMonitor.prometheusRule.rules }}
  groups:
  - name: {{ template "loki.fullname" . }}
    rules: {{- toYaml .Values.serviceMonitor.prometheusRule.rules | nindent 4 }}
{{- end }}
{{- end }}
