{{- if .Values.ingress.enabled -}}
{{- $fullName := include "loki.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $apiVersion := include "ingress.apiVersion" . -}}
apiVersion: {{ $apiVersion }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
{{- end }}
{{- if .Values.ingress.tls }}
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .paths }}
          - path: {{ . }}
            {{- if eq $apiVersion "networking.k8s.io/v1" }}
            pathType: Prefix
              {{- end }}
            backend:
              {{- if eq $apiVersion "networking.k8s.io/v1" }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $svcPort }}
              {{- end }}
        {{- end }}
  {{- end }}
{{- end }}
