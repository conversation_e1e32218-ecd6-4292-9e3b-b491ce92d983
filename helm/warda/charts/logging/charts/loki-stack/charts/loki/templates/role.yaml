{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
{{- if .Values.rbac.pspEnabled }}
rules:
- apiGroups:      ['extensions']
  resources:      ['podsecuritypolicies']
  verbs:          ['use']
  resourceNames:  [{{ template "loki.fullname" . }}]
{{- end }}
{{- end }}

