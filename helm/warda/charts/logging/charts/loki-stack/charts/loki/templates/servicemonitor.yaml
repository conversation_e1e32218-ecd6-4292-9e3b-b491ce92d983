{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "loki.fullname" . }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
    {{- if .Values.serviceMonitor.additionalLabels }}
{{ toYaml .Values.serviceMonitor.additionalLabels | indent 4 }}
    {{- end }}
  {{- if .Values.serviceMonitor.annotations }}
  annotations:
{{ toYaml .Values.serviceMonitor.annotations | indent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      app: {{ template "loki.name" . }}
      release: {{ .Release.Name | quote }}
      variant: headless
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace | quote }}
  endpoints:
  - port: http-metrics
    {{- if .Values.serviceMonitor.interval }}
    interval: {{ .Values.serviceMonitor.interval }}
    {{- end }}
    {{- if .Values.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.serviceMonitor.scrapeTimeout }}
    {{- end }}
    {{- if .Values.serviceMonitor.path }}
    path: {{ .Values.serviceMonitor.path }}
    {{- end }}
    {{- with .Values.serviceMonitor.scheme }}
    scheme: {{ . }}
    {{- end }}
    {{- with .Values.serviceMonitor.tlsConfig }}
    tlsConfig: 
        {{- toYaml . | nindent 6 }}
    {{- end }}
{{- end }}
