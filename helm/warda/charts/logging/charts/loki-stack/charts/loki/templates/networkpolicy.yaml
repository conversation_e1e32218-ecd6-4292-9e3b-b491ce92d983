{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      name: {{ template "loki.fullname" . }}
      app: {{ template "loki.name" . }}
      release: {{ .Release.Name }}
  ingress:
    - from:
      - podSelector:
          matchLabels:
            app: {{ template "client.name" . }}
            release: {{ .Release.Name }}
    - ports:
      - port: {{ .Values.service.port }}
{{- end }}
