{{- if .Values.podDisruptionBudget -}}
apiVersion: {{ include "loki.podDisruptionBudget.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app: {{ template "loki.name" . }}
{{ toYaml .Values.podDisruptionBudget | indent 2 }}
{{- end }}
