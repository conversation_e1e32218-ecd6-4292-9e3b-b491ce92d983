apiVersion: v1
kind: Service
metadata:
  name: {{ template "loki.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "loki.labels" . | nindent 4 }}
    {{- with .Values.service.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    {{- toYaml .Values.service.annotations | nindent 4 }}
spec:
{{- if .Values.service.loadBalancerSourceRanges }}
   externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy }}
{{- end }}
  type: {{ .Values.service.type }}
{{- if (and (eq .Values.service.type "ClusterIP") (not (empty .Values.service.clusterIP))) }}
  clusterIP: {{ .Values.service.clusterIP }}
{{- end }}
{{- if (and (eq .Values.service.type "LoadBalancer") (not (empty .Values.service.loadBalancerIP))) }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
{{- end }}
{{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range $cidr := .Values.service.loadBalancerSourceRanges }}
    - {{ $cidr }}
  {{- end }}
{{- end }}
  ports:
    - port: {{ .Values.service.port }}
      protocol: TCP
      name: http-metrics
      targetPort: {{ .Values.service.targetPort }}
{{- if (and (eq .Values.service.type "NodePort") (not (empty .Values.service.nodePort))) }}
      nodePort: {{ .Values.service.nodePort }}
{{- end }}
{{- if .Values.extraPorts }}
{{ toYaml .Values.extraPorts | indent 4}}
{{- end }}
  selector:
    app: {{ template "loki.name" . }}
    release: {{ .Release.Name }}
