{{- if .Values.podDisruptionBudget }}
apiVersion: {{ include "alertmanager.pdb.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "alertmanager.fullname" . }}
  labels:
    {{- include "alertmanager.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "alertmanager.selectorLabels" . | nindent 6 }}
  {{- toYaml .Values.podDisruptionBudget | nindent 2 }}
{{- end }}
