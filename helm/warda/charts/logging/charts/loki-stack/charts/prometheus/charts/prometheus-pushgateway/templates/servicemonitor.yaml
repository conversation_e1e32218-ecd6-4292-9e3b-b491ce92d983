{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    {{- include "prometheus-pushgateway.defaultLabels" . | nindent 4 }}
    {{- if .Values.serviceMonitor.additionalLabels }}
    {{- toYaml .Values.serviceMonitor.additionalLabels | nindent 4 }}
    {{- end }}
  name: {{ include "prometheus-pushgateway.fullname" .  }}
  {{- with .Values.serviceMonitor.namespace }}
  namespace: {{ . }}
  {{- end }}
spec:
  endpoints:
  - port: http
    {{- with .Values.serviceMonitor.interval }}
    interval: {{ . }}
    {{- end }}
    {{- with .Values.serviceMonitor.scheme }}
    scheme: {{ . }}
    {{- end }}
    {{- with .Values.serviceMonitor.bearerTokenFile }}
    bearerTokenFile: {{ . }}
    {{- end }}
    {{- with .Values.serviceMonitor.tlsConfig }}
    tlsConfig:
      {{- toYaml .| nindent 6 }}
    {{- end }}
    {{- with .Values.serviceMonitor.scrapeTimeout }}
    scrapeTimeout: {{ . }}
    {{- end }}
    path: /metrics
    honorLabels: {{ .Values.serviceMonitor.honorLabels }}
    {{- with .Values.serviceMonitor.metricRelabelings }}
    metricRelabelings:
      {{- tpl (toYaml . | nindent 6) . }}
    {{- end }}
    {{- with .Values.serviceMonitor.relabelings }}
    relabelings:
      {{- toYaml . | nindent 6 }}
    {{- end }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  selector:
    matchLabels:
      {{- include "prometheus-pushgateway.selectorLabels" . | nindent 6 }}
{{- end -}}
