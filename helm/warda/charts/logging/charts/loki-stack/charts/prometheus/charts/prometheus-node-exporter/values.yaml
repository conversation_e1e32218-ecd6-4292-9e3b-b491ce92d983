# Default values for prometheus-node-exporter.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
image:
  repository: quay.io/prometheus/node-exporter
  # Overrides the image tag whose default is {{ printf "v%s" .Chart.AppVersion }}
  tag: ""
  pullPolicy: IfNotPresent
  sha: ""

imagePullSecrets: []
# - name: "image-pull-secret"

service:
  type: ClusterIP
  port: 9100
  targetPort: 9100
  nodePort:
  portName: metrics
  listenOnAllInterfaces: true
  annotations:
    prometheus.io/scrape: "true"

# Additional environment variables that will be passed to the daemonset
env: {}
##  env:
##    VARIABLE: value

prometheus:
  monitor:
    enabled: false
    additionalLabels: {}
    namespace: ""

    jobLabel: ""

    scheme: http
    basicAuth: {}
    bearerTokenFile:
    tlsConfig: {}

    ## proxyUrl: URL of a proxy that should be used for scraping.
    ##
    proxyUrl: ""

    ## Override serviceMonitor selector
    ##
    selectorOverride: {}

    relabelings: []
    metricRelabelings: []
    interval: ""
    scrapeTimeout: 10s
    ## prometheus.monitor.apiVersion ApiVersion for the serviceMonitor Resource(defaults to "monitoring.coreos.com/v1")
    apiVersion: ""

    ## SampleLimit defines per-scrape limit on number of scraped samples that will be accepted.
    ##
    sampleLimit: 0

    ## TargetLimit defines a limit on the number of scraped targets that will be accepted.
    ##
    targetLimit: 0

    ## Per-scrape limit on number of labels that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelLimit: 0

    ## Per-scrape limit on length of labels name that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelNameLengthLimit: 0

    ## Per-scrape limit on length of labels value that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelValueLengthLimit: 0

## Customize the updateStrategy if set
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 200m
  #   memory: 50Mi
  # requests:
  #   cpu: 100m
  #   memory: 30Mi

serviceAccount:
  # Specifies whether a ServiceAccount should be created
  create: true
  # The name of the ServiceAccount to use.
  # If not set and create is true, a name is generated using the fullname template
  name:
  annotations: {}
  imagePullSecrets: []
  automountServiceAccountToken: false

securityContext:
  fsGroup: 65534
  runAsGroup: 65534
  runAsNonRoot: true
  runAsUser: 65534

containerSecurityContext: {}
  # capabilities:
  #   add:
  #   - SYS_TIME

rbac:
  ## If true, create & use RBAC resources
  ##
  create: true
  ## If true, create & use Pod Security Policy resources
  ## https://kubernetes.io/docs/concepts/policy/pod-security-policy/
  pspEnabled: true
  pspAnnotations: {}

# for deployments that have node_exporter deployed outside of the cluster, list
# their addresses here
endpoints: []

# Expose the service to the host network
hostNetwork: true

# Share the host process ID namespace
hostPID: true

# Mount the node's root file system (/) at /host/root in the container
hostRootFsMount:
  enabled: true
  # Defines how new mounts in existing mounts on the node or in the container
  # are propagated to the container or node, respectively. Possible values are
  # None, HostToContainer, and Bidirectional. If this field is omitted, then
  # None is used. More information on:
  # https://kubernetes.io/docs/concepts/storage/volumes/#mount-propagation
  mountPropagation: HostToContainer

## Assign a group of affinity scheduling rules
##
affinity: {}
#   nodeAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#       nodeSelectorTerms:
#         - matchFields:
#             - key: metadata.name
#               operator: In
#               values:
#                 - target-host-name

# Annotations to be added to node exporter pods
podAnnotations:
  # Fix for very slow GKE cluster upgrades
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"

# Extra labels to be added to node exporter pods
podLabels: {}

# Annotations to be added to node exporter daemonset
daemonsetAnnotations: {}

## set to true to add the release label so scraping of the servicemonitor with kube-prometheus-stack works out of the box
releaseLabel: false

# Custom DNS configuration to be added to prometheus-node-exporter pods
dnsConfig: {}
# nameservers:
#   - *******
# searches:
#   - ns1.svc.cluster-domain.example
#   - my.dns.search.suffix
# options:
#   - name: ndots
#     value: "2"
#   - name: edns0

## Assign a nodeSelector if operating a hybrid cluster
##
nodeSelector: {}
#   beta.kubernetes.io/arch: amd64
#   beta.kubernetes.io/os: linux

tolerations:
  - effect: NoSchedule
    operator: Exists

## Assign a PriorityClassName to pods if set
# priorityClassName: ""

## Additional container arguments
##
extraArgs: []
#   - --collector.diskstats.ignored-devices=^(ram|loop|fd|(h|s|v)d[a-z]|nvme\\d+n\\d+p)\\d+$
#   - --collector.textfile.directory=/run/prometheus

## Additional mounts from the host to node-exporter container
##
extraHostVolumeMounts: []
#  - name: <mountName>
#    hostPath: <hostPath>
#    mountPath: <mountPath>
#    readOnly: true|false
#    mountPropagation: None|HostToContainer|Bidirectional

## Additional configmaps to be mounted.
##
configmaps: []
# - name: <configMapName>
#   mountPath: <mountPath>
secrets: []
# - name: <secretName>
#   mountPath: <mountPatch>
## Override the deployment namespace
##
namespaceOverride: ""

## Additional containers for export metrics to text file
##
sidecars: []
##  - name: nvidia-dcgm-exporter
##    image: nvidia/dcgm-exporter:1.4.3

## Volume for sidecar containers
##
sidecarVolumeMount: []
##  - name: collector-textfiles
##    mountPath: /run/prometheus
##    readOnly: false

## Additional mounts from the host to sidecar containers
##
sidecarHostVolumeMounts: []
#  - name: <mountName>
#    hostPath: <hostPath>
#    mountPath: <mountPath>
#    readOnly: true|false
#    mountPropagation: None|HostToContainer|Bidirectional

## Additional InitContainers to initialize the pod
##
extraInitContainers: []

## Liveness probe
##
livenessProbe:
  failureThreshold: 3
  httpGet:
    httpHeaders: []
    scheme: http
  initialDelaySeconds: 0
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

## Readiness probe
##
readinessProbe:
  failureThreshold: 3
  httpGet:
    httpHeaders: []
    scheme: http
  initialDelaySeconds: 0
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1

# Enable vertical pod autoscaler support for prometheus-node-exporter
verticalPodAutoscaler:
  enabled: false
  # List of resources that the vertical pod autoscaler can control. Defaults to cpu and memory
  controlledResources: []

  # Define the max allowed resources for the pod
  maxAllowed: {}
  # cpu: 200m
  # memory: 100Mi
  # Define the min allowed resources for the pod
  minAllowed: {}
  # cpu: 200m
  # memory: 100Mi

  # updatePolicy:
    # Specifies whether recommended updates are applied when a Pod is started and whether recommended updates
    # are applied during the life of a Pod. Possible values are "Off", "Initial", "Recreate", and "Auto".
    # updateMode: Auto
