{{- if and .Values.rbac.create .Values.rbac.pspEnabled (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
  {{- with .Values.rbac.pspAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  privileged: false
  # Allow core volume types.
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
    - 'hostPath'
  hostNetwork: true
  hostIPC: false
  hostPID: true
  hostPorts:
    - min: 0
      max: 65535
  runAsUser:
    # Permits the container to run with root privileges as well.
    rule: 'RunAsAny'
  seLinux:
    # This policy assumes the nodes are using AppArmor rather than SELinux.
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      # Allow adding the root group.
      - min: 0
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      # Allow adding the root group.
      - min: 0
        max: 65535
  readOnlyRootFilesystem: false
{{- end }}
