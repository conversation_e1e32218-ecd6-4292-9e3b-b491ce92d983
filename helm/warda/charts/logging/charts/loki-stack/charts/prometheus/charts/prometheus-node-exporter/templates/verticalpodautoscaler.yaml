{{- if and (.Capabilities.APIVersions.Has "autoscaling.k8s.io/v1") (.Values.verticalPodAutoscaler.enabled) }}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
spec:
  resourcePolicy:
    containerPolicies:
    - containerName: {{ include "prometheus-node-exporter.name" . }}
      {{- with .Values.verticalPodAutoscaler.controlledResources }}
      controlledResources: {{ . }}
      {{- end }}
      {{- with .Values.verticalPodAutoscaler.maxAllowed }}
      maxAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.verticalPodAutoscaler.minAllowed }}
      minAllowed:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  targetRef:
    apiVersion: apps/v1
    kind: DaemonSet
    name:  {{ include "prometheus-node-exporter.fullname" . }}
  {{- if .Values.verticalPodAutoscaler.updatePolicy }}
  updatePolicy:
    {{- with .Values.verticalPodAutoscaler.updatePolicy.updateMode }}
    updateMode: {{ . }}
    {{- end }}
  {{- end }}
{{- end }}
