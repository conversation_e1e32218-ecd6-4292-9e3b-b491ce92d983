{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"alertRelabelConfigs": {"type": "object"}, "alertmanager": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "persistence": {"type": "object", "properties": {"size": {"type": "string"}}}, "podSecurityContext": {"type": "object", "properties": {"fsGroup": {"type": "integer"}, "runAsGroup": {"type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"type": "integer"}}}}}, "configmapReload": {"type": "object", "properties": {"prometheus": {"type": "object", "properties": {"containerSecurityContext": {"type": "object"}, "enabled": {"type": "boolean"}, "extraArgs": {"type": "object"}, "extraConfigmapMounts": {"type": "array"}, "extraVolumeDirs": {"type": "array"}, "image": {"type": "object", "properties": {"digest": {"type": "string"}, "pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "name": {"type": "string"}, "resources": {"type": "object"}}}}}, "extraManifests": {"type": "array"}, "extraScrapeConfigs": {"type": "string"}, "forceNamespace": {"type": "string"}, "imagePullSecrets": {"type": "array"}, "kube-state-metrics": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}, "networkPolicy": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}, "podSecurityPolicy": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}, "prometheus-node-exporter": {"type": "object", "properties": {"containerSecurityContext": {"type": "object", "properties": {"allowPrivilegeEscalation": {"type": "boolean"}}}, "enabled": {"type": "boolean"}, "rbac": {"type": "object", "properties": {"pspEnabled": {"type": "boolean"}}}}}, "prometheus-pushgateway": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "serviceAnnotations": {"type": "object", "properties": {"prometheus.io/probe": {"type": "string"}}}}}, "rbac": {"type": "object", "properties": {"create": {"type": "boolean"}}}, "ruleFiles": {"type": "object"}, "server": {"type": "object", "properties": {"affinity": {"type": "object"}, "alertmanagers": {"type": "array"}, "baseURL": {"type": "string"}, "configMapOverrideName": {"type": "string"}, "configPath": {"type": "string"}, "containerSecurityContext": {"type": "object"}, "defaultFlagsOverride": {"type": "array"}, "deploymentAnnotations": {"type": "object"}, "dnsConfig": {"type": "object"}, "dnsPolicy": {"type": "string"}, "emptyDir": {"type": "object", "properties": {"sizeLimit": {"type": "string"}}}, "enableServiceLinks": {"type": "boolean"}, "env": {"type": "array"}, "extraArgs": {"type": "object"}, "extraConfigmapLabels": {"type": "object"}, "extraConfigmapMounts": {"type": "array"}, "extraFlags": {"type": "array", "items": {"type": "string"}}, "extraHostPathMounts": {"type": "array"}, "extraInitContainers": {"type": "array"}, "extraSecretMounts": {"type": "array"}, "extraVolumeMounts": {"type": "array"}, "extraVolumes": {"type": "array"}, "global": {"type": "object", "properties": {"evaluation_interval": {"type": "string"}, "scrape_interval": {"type": "string"}, "scrape_timeout": {"type": "string"}}}, "hostAliases": {"type": "array"}, "hostNetwork": {"type": "boolean"}, "image": {"type": "object", "properties": {"digest": {"type": "string"}, "pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object"}, "enabled": {"type": "boolean"}, "extraLabels": {"type": "object"}, "extraPaths": {"type": "array"}, "hosts": {"type": "array"}, "path": {"type": "string"}, "pathType": {"type": "string"}, "tls": {"type": "array"}}}, "livenessProbeFailureThreshold": {"type": "integer"}, "livenessProbeInitialDelay": {"type": "integer"}, "livenessProbePeriodSeconds": {"type": "integer"}, "livenessProbeSuccessThreshold": {"type": "integer"}, "livenessProbeTimeout": {"type": "integer"}, "name": {"type": "string"}, "nodeSelector": {"type": "object"}, "persistentVolume": {"type": "object", "properties": {"accessModes": {"type": "array", "items": {"type": "string"}}, "annotations": {"type": "object"}, "enabled": {"type": "boolean"}, "existingClaim": {"type": "string"}, "labels": {"type": "object"}, "mountPath": {"type": "string"}, "size": {"type": "string"}, "subPath": {"type": "string"}}}, "podAnnotations": {"type": "object"}, "podDisruptionBudget": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "maxUnavailable": {"type": "integer"}}}, "podLabels": {"type": "object"}, "podSecurityPolicy": {"type": "object", "properties": {"annotations": {"type": "object"}}}, "prefixURL": {"type": "string"}, "priorityClassName": {"type": "string"}, "probeHeaders": {"type": "array"}, "probeScheme": {"type": "string"}, "readinessProbeFailureThreshold": {"type": "integer"}, "readinessProbeInitialDelay": {"type": "integer"}, "readinessProbePeriodSeconds": {"type": "integer"}, "readinessProbeSuccessThreshold": {"type": "integer"}, "readinessProbeTimeout": {"type": "integer"}, "remoteRead": {"type": "array"}, "remoteWrite": {"type": "array"}, "replicaCount": {"type": "integer"}, "resources": {"type": "object"}, "retention": {"type": "string"}, "securityContext": {"type": "object", "properties": {"fsGroup": {"type": "integer"}, "runAsGroup": {"type": "integer"}, "runAsNonRoot": {"type": "boolean"}, "runAsUser": {"type": "integer"}}}, "service": {"type": "object", "properties": {"annotations": {"type": "object"}, "clusterIP": {"type": "string"}, "enabled": {"type": "boolean"}, "externalIPs": {"type": "array"}, "gRPC": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "servicePort": {"type": "integer"}}}, "labels": {"type": "object"}, "loadBalancerIP": {"type": "string"}, "loadBalancerSourceRanges": {"type": "array"}, "servicePort": {"type": "integer"}, "sessionAffinity": {"type": "string"}, "statefulsetReplica": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "replica": {"type": "integer"}}}, "type": {"type": "string"}}}, "sidecarContainers": {"type": "object"}, "sidecarTemplateValues": {"type": "object"}, "startupProbe": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "failureThreshold": {"type": "integer"}, "periodSeconds": {"type": "integer"}, "timeoutSeconds": {"type": "integer"}}}, "statefulSet": {"type": "object", "properties": {"annotations": {"type": "object"}, "enabled": {"type": "boolean"}, "headless": {"type": "object", "properties": {"annotations": {"type": "object"}, "gRPC": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "servicePort": {"type": "integer"}}}, "labels": {"type": "object"}, "servicePort": {"type": "integer"}}}, "labels": {"type": "object"}, "podManagementPolicy": {"type": "string"}}}, "storagePath": {"type": "string"}, "strategy": {"type": "object", "properties": {"type": {"type": "string"}}}, "tcpSocketProbeEnabled": {"type": "boolean"}, "terminationGracePeriodSeconds": {"type": "integer"}, "tolerations": {"type": "array"}, "verticalAutoscaler": {"type": "object", "properties": {"enabled": {"type": "boolean"}}}}}, "serverFiles": {"type": "object", "properties": {"alerting_rules.yml": {"type": "object"}, "alerts": {"type": "object"}, "prometheus.yml": {"type": "object", "properties": {"rule_files": {"type": "array", "items": {"type": "string"}}, "scrape_configs": {"type": "array", "items": {"type": "object", "properties": {"job_name": {"type": "string"}, "static_configs": {"type": "array", "items": {"type": "object", "properties": {"targets": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "recording_rules.yml": {"type": "object"}, "rules": {"type": "object"}}}, "serviceAccounts": {"type": "object", "properties": {"server": {"type": "object", "properties": {"annotations": {"type": "object"}, "create": {"type": "boolean"}, "name": {"type": "string"}}}}}}}