apiVersion: v1
kind: Service
metadata:
  name: {{ include "prometheus-node-exporter.fullname" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels:
    {{- include "prometheus-node-exporter.labels" $ | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      {{- if ( and (eq .Values.service.type "NodePort" ) (not (empty .Values.service.nodePort)) ) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: {{ .Values.service.portName }}
  selector:
    {{- include "prometheus-node-exporter.selectorLabels" . | nindent 4 }}
