{{- if .Values.networkPolicy.enabled }}
apiVersion: {{ template "prometheus.networkPolicy.apiVersion" . }}
kind: NetworkPolicy
metadata:
  name: {{ template "prometheus.server.fullname" . }}
{{ include "prometheus.namespace" . | indent 2 }}
  labels:
    {{- include "prometheus.server.labels" . | nindent 4 }}
spec:
  podSelector:
    matchLabels:
      {{- include "prometheus.server.matchLabels" . | nindent 6 }}
  ingress:
    - ports:
      - port: 9090
{{- end }}
