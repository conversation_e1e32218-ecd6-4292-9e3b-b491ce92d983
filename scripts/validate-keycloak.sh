#!/bin/bash

# Validation script for Keycloak setup
set -e

echo "🔍 Validating Keycloak setup..."

# Check if minikube is running
if ! minikube status | grep -q "Running"; then
    echo "❌ Minikube is not running"
    exit 1
fi

# Check if Helm release exists
if ! helm list | grep -q "warda"; then
    echo "❌ Warda Helm release not found"
    echo "Run: helm install warda helm/warda"
    exit 1
fi

echo "✅ Helm release found"

# Check if pods are running
echo "📋 Checking pod status..."
kubectl get pods

# Check if Keycloak pod is ready
if kubectl get pods -l app.kubernetes.io/name=keycloak | grep -q "Running"; then
    echo "✅ Keycloak pod is running"
else
    echo "❌ Keycloak pod is not running"
    echo "Check logs: kubectl logs -l app.kubernetes.io/name=keycloak"
fi

# Check if PostgreSQL pod is ready
if kubectl get pods -l app.kubernetes.io/name=postgresql | grep -q "Running"; then
    echo "✅ PostgreSQL pod is running"
else
    echo "❌ PostgreSQL pod is not running"
    echo "Check logs: kubectl logs -l app.kubernetes.io/name=postgresql"
fi

# Check services
echo "📋 Checking services..."
kubectl get svc

# Check ingress
echo "📋 Checking ingress..."
kubectl get ingress

# Get minikube IP
MINIKUBE_IP=$(minikube ip)
echo "🌐 Minikube IP: $MINIKUBE_IP"

# Check if hosts entries exist
echo "📋 Checking /etc/hosts entries..."
if grep -q "keycloak.local" /etc/hosts; then
    echo "✅ keycloak.local entry found in /etc/hosts"
else
    echo "❌ keycloak.local entry missing from /etc/hosts"
    echo "Add: echo '$MINIKUBE_IP keycloak.local' | sudo tee -a /etc/hosts"
fi

# Test Keycloak connectivity
echo "🔗 Testing Keycloak connectivity..."
if curl -s -o /dev/null -w "%{http_code}" http://keycloak.local/realms/master | grep -q "200"; then
    echo "✅ Keycloak is accessible at http://keycloak.local"
else
    echo "❌ Keycloak is not accessible"
    echo "Try port forwarding: kubectl port-forward svc/warda-keycloak 8080:8080"
fi

echo ""
echo "🎯 Summary:"
echo "- Keycloak Admin: http://keycloak.local (admin/admin123)"
echo "- Realm: warda"
echo "- Frontend Client: warda-frontend"
echo "- Backend Client: warda-backend"
echo "- Test User: testuser/password"
