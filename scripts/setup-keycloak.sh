#!/bin/bash

# Setup script for Key<PERSON>loak in Minikube
set -e

echo "🚀 Setting up Key<PERSON>loak in Minikube for Warda platform"

# Check if minikube is running
if ! minikube status | grep -q "Running"; then
    echo "❌ Minikube is not running. Please start minikube first:"
    echo "   minikube start"
    exit 1
fi

# Enable ingress addon if not already enabled
echo "📡 Enabling ingress addon..."
minikube addons enable ingress

# Update Helm dependencies
echo "📦 Updating Helm dependencies..."
cd helm/warda
helm dependency update

# Install or upgrade the Helm chart
echo "🎯 Installing/upgrading Warda platform with Keycloak..."
helm upgrade --install warda . --wait --timeout=10m

# Get minikube IP
MINIKUBE_IP=$(minikube ip)

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Add the following entries to your /etc/hosts file:"
echo "   $MINIKUBE_IP keycloak.local"
echo "   $MINIKUBE_IP frontend.local"
echo "   $MINIKUBE_IP backend.local"
echo ""
echo "2. Wait for all pods to be ready:"
echo "   kubectl get pods -w"
echo ""
echo "3. Access Keycloak admin console:"
echo "   URL: http://keycloak.local"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "4. The 'warda' realm will be automatically configured with:"
echo "   - Frontend client: warda-frontend (public)"
echo "   - Backend client: warda-backend (confidential)"
echo "   - Test user: testuser / password"
echo ""
echo "5. Check service status:"
echo "   kubectl get svc"
echo "   kubectl get ingress"
echo ""
echo "🔧 Troubleshooting:"
echo "   - Check pod logs: kubectl logs -l app.kubernetes.io/name=keycloak"
echo "   - Check ingress: kubectl describe ingress"
echo "   - Port forward if ingress issues: kubectl port-forward svc/warda-keycloak 8080:8080"
