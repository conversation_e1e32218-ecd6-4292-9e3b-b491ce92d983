#!/bin/bash
set -e

# Navigate to the project root
cd "$(dirname "$0")/.."

# Build the frontend
echo "🚀 Building frontend..."
cd apps/frontend

# Install wasm target if not already installed
if ! rustup target list | grep -q "wasm32-unknown-unknown (installed)"; then
    echo "🔧 Installing wasm32-unknown-unknown target..."
    rustup target add wasm32-unknown-unknown
fi

# Install trunk if not already installed
if ! command -v trunk &> /dev/null; then
    echo "🔧 Installing trunk..."
    cargo install --locked trunk
fi

# Build the frontend for web
echo "🛠️  Building frontend..."
trunk build --release

# Build the Docker image
echo "🐳 Building Docker image..."
docker build -t warda-frontend .

# Load the image into Minikube
echo "⬆️  Loading image into Minikube..."
minikube image load warda-frontend

# Deploy using Helm
echo "🚀 Deploying frontend using Helm..."
cd "$OLDPWD"  # Go back to project root
helm upgrade --install frontend ./helm/warda/charts/frontend \
  --namespace frontend \
  --create-namespace \
  --set image.repository=warda-frontend \
  --set image.tag=latest \
  --set image.pullPolicy=Never \
  --set service.type=NodePort \
  --set service.nodePort=30080

# Get the URL
MINIKUBE_IP=$(minikube ip)
FRONTEND_URL="http://$MINIKUBE_IP:30080"

echo ""
echo "✅ Frontend deployed successfully!"
echo "🌐 Access the frontend at: $FRONTEND_URL"
echo ""
echo "To open in your default browser, run:"
echo "open $FRONTEND_URL"
echo ""
echo "To view logs, run:"
echo "kubectl logs -n frontend deployment/frontend"
echo ""
echo "To delete the deployment, run:"
echo "helm uninstall frontend -n frontend"
echo ""
