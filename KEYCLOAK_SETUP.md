# Keycloak OAuth Setup for Warda Platform

This guide explains how to set up Key<PERSON>loak for OAuth authentication in your Warda platform running on Minikube.

## Overview

The setup includes:
- **Keycloak** identity and access management server
- **PostgreSQL database** for Keycloak (shared with your app)
- **Two OAuth clients**:
  - `warda-frontend`: Public client for your frontend SPA
  - `warda-backend`: Confidential client for your backend API
- **Pre-configured realm** with test users

## Quick Setup

1. **Run the setup script:**
   ```bash
   ./scripts/setup-keycloak.sh
   ```

2. **Add hosts entries** (replace `<MINIKUBE_IP>` with your minikube IP):
   ```bash
   echo "<MINIKUBE_IP> keycloak.local" | sudo tee -a /etc/hosts
   echo "<MINIKUBE_IP> frontend.local" | sudo tee -a /etc/hosts
   echo "<MINIKUBE_IP> backend.local" | sudo tee -a /etc/hosts
   ```

3. **Access Keycloak:**
   - URL: http://keycloak.local
   - Admin username: `admin`
   - Admin password: `admin123`

## Manual Setup

If you prefer manual setup:

1. **Update Helm dependencies:**
   ```bash
   cd helm/warda
   helm dependency update
   ```

2. **Install the chart:**
   ```bash
   helm upgrade --install warda . --wait --timeout=10m
   ```

3. **Enable ingress (if not already enabled):**
   ```bash
   minikube addons enable ingress
   ```

## Configuration Details

### Frontend Client (`warda-frontend`)
- **Type:** Public client (no client secret)
- **Allowed flows:** Authorization Code with PKCE
- **Redirect URIs:**
  - `http://localhost:3000/*`
  - `http://frontend.local/*`
- **Web Origins:**
  - `http://localhost:3000`
  - `http://frontend.local`

### Backend Client (`warda-backend`)
- **Type:** Confidential client
- **Client Secret:** `backend-client-secret`
- **Allowed flows:** Authorization Code, Direct Access Grants, Service Accounts
- **Redirect URIs:**
  - `http://localhost:8080/*`
  - `http://backend.local/*`

### Test User
- **Username:** `testuser`
- **Password:** `password`
- **Email:** `<EMAIL>`
- **Role:** `user`

## Environment Variables

### Frontend Environment Variables
Your frontend will have access to:
- `KEYCLOAK_URL`: `http://keycloak.local`
- `KEYCLOAK_REALM`: `warda`
- `KEYCLOAK_CLIENT_ID`: `warda-frontend`

### Backend Environment Variables
Your backend will have access to:
- `KEYCLOAK_URL`: `http://keycloak:8080`
- `KEYCLOAK_REALM`: `warda`
- `KEYCLOAK_CLIENT_ID`: `warda-backend`
- `KEYCLOAK_CLIENT_SECRET`: `backend-client-secret`
- `KEYCLOAK_ISSUER`: `http://keycloak:8080/realms/warda`

## Integration Examples

### Frontend (JavaScript/TypeScript)
```javascript
import Keycloak from 'keycloak-js';

const keycloak = new Keycloak({
  url: process.env.KEYCLOAK_URL,
  realm: process.env.KEYCLOAK_REALM,
  clientId: process.env.KEYCLOAK_CLIENT_ID,
});

// Initialize
keycloak.init({
  onLoad: 'login-required',
  checkLoginIframe: false,
}).then((authenticated) => {
  if (authenticated) {
    console.log('User is authenticated');
    // Access token: keycloak.token
  }
});
```

### Backend (Rust)
Add to your `Cargo.toml`:
```toml
[dependencies]
jsonwebtoken = "8.3"
serde = { version = "1.0", features = ["derive"] }
reqwest = { version = "0.11", features = ["json"] }
```

Example token validation:
```rust
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

// Get JWKS from Keycloak
let jwks_url = format!("{}/realms/{}/protocol/openid-connect/certs", 
    env::var("KEYCLOAK_URL").unwrap(),
    env::var("KEYCLOAK_REALM").unwrap()
);

// Validate JWT token
fn validate_token(token: &str) -> Result<Claims, Error> {
    // Implementation depends on your JWT library
    // Verify against Keycloak's public key
}
```

## Troubleshooting

### Common Issues

1. **Pods not starting:**
   ```bash
   kubectl get pods
   kubectl logs -l app.kubernetes.io/name=keycloak
   ```

2. **Ingress not working:**
   ```bash
   kubectl get ingress
   kubectl describe ingress warda-keycloak
   ```

3. **Database connection issues:**
   ```bash
   kubectl logs -l app.kubernetes.io/name=postgresql
   ```

### Port Forwarding (Alternative Access)
If ingress is not working:
```bash
# Keycloak
kubectl port-forward svc/warda-keycloak 8080:8080

# Access at: http://localhost:8080
```

### Reset Keycloak Data
To start fresh:
```bash
helm uninstall warda
kubectl delete pvc --all
helm install warda helm/warda
```

## Security Notes

⚠️ **Important for Production:**
- Change default admin password
- Use proper TLS certificates
- Configure proper CORS settings
- Use secure client secrets
- Enable proper session management
- Configure rate limiting

## Next Steps

1. **Integrate with your frontend** using a Keycloak JavaScript adapter
2. **Integrate with your backend** for JWT token validation
3. **Configure user roles and permissions** in Keycloak admin console
4. **Set up user registration flows** if needed
5. **Configure social login providers** (Google, GitHub, etc.)

For more advanced configuration, visit the Keycloak admin console at http://keycloak.local
